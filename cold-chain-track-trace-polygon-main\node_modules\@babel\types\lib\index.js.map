{"version": 3, "names": ["_isReactComponent", "require", "_isCompatTag", "_buildC<PERSON><PERSON>n", "_assertNode", "_index", "Object", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call", "_exportNames", "exports", "defineProperty", "enumerable", "get", "_createTypeAnnotationBasedOnTypeof", "_createFlowUnionType", "_createTSUnionType", "_productions", "_index2", "_cloneNode", "_clone", "_cloneDeep", "_cloneDeepWithoutLoc", "_cloneWithoutLoc", "_addComment", "_addComments", "_inheritInnerComments", "_inheritLeadingComments", "_inheritsComments", "_inheritTrailingComments", "_removeComments", "_index3", "_index4", "_ensureBlock", "_toBindingIdentifierName", "_toBlock", "_toCom<PERSON><PERSON>ey", "_toExpression", "_toIdentifier", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toStatement", "_valueToNode", "_index5", "_appendToMemberExpression", "_inherits", "_prependToMemberExpression", "_removeProperties", "_removePropertiesDeep", "_removeTypeDuplicates", "_getAssignmentIdentifiers", "_getBindingIdentifiers", "_getOuterBindingIdentifiers", "_getFunctionName", "_traverse", "_traverseFast", "_shallowEqual", "_is", "_isBinding", "_isBlockScoped", "_isImmutable", "_isLet", "_isNode", "_isNodesEquivalent", "_isPlaceholderType", "_isReferenced", "_isScope", "_isSpecifierDefault", "_isType", "_isValidES3Identifier", "_isValidIdentifier", "_isVar", "_matchesPattern", "_validate", "_buildMatchMemberExpression", "_index6", "_deprecationWarning", "_toSequenceExpression", "react", "isReactComponent", "isCompatTag", "buildChildren", "toSequenceExpression", "process", "env", "BABEL_TYPES_8_BREAKING", "console", "warn"], "sources": ["../src/index.ts"], "sourcesContent": ["import isReactComponent from \"./validators/react/isReactComponent.ts\";\nimport isCompatTag from \"./validators/react/isCompatTag.ts\";\nimport buildChildren from \"./builders/react/buildChildren.ts\";\n\n// asserts\nexport { default as assertNode } from \"./asserts/assertNode.ts\";\nexport * from \"./asserts/generated/index.ts\";\n\n// builders\nexport { default as createTypeAnnotationBasedOnTypeof } from \"./builders/flow/createTypeAnnotationBasedOnTypeof.ts\";\n/** @deprecated use createFlowUnionType instead */\nexport { default as createUnionTypeAnnotation } from \"./builders/flow/createFlowUnionType.ts\";\nexport { default as createFlowUnionType } from \"./builders/flow/createFlowUnionType.ts\";\nexport { default as createTSUnionType } from \"./builders/typescript/createTSUnionType.ts\";\nexport * from \"./builders/productions.ts\";\nexport * from \"./builders/generated/index.ts\"; // includes AST types\n\n// clone\nexport { default as cloneNode } from \"./clone/cloneNode.ts\";\nexport { default as clone } from \"./clone/clone.ts\";\nexport { default as cloneDeep } from \"./clone/cloneDeep.ts\";\nexport { default as cloneDeepWithoutLoc } from \"./clone/cloneDeepWithoutLoc.ts\";\nexport { default as cloneWithoutLoc } from \"./clone/cloneWithoutLoc.ts\";\n\n// comments\nexport { default as addComment } from \"./comments/addComment.ts\";\nexport { default as addComments } from \"./comments/addComments.ts\";\nexport { default as inheritInnerComments } from \"./comments/inheritInnerComments.ts\";\nexport { default as inheritLeadingComments } from \"./comments/inheritLeadingComments.ts\";\nexport { default as inheritsComments } from \"./comments/inheritsComments.ts\";\nexport { default as inheritTrailingComments } from \"./comments/inheritTrailingComments.ts\";\nexport { default as removeComments } from \"./comments/removeComments.ts\";\n\n// constants\nexport * from \"./constants/generated/index.ts\";\nexport * from \"./constants/index.ts\";\n\n// converters\nexport { default as ensureBlock } from \"./converters/ensureBlock.ts\";\nexport { default as toBindingIdentifierName } from \"./converters/toBindingIdentifierName.ts\";\nexport { default as toBlock } from \"./converters/toBlock.ts\";\nexport { default as toComputedKey } from \"./converters/toComputedKey.ts\";\nexport { default as toExpression } from \"./converters/toExpression.ts\";\nexport { default as toIdentifier } from \"./converters/toIdentifier.ts\";\nexport { default as toKeyAlias } from \"./converters/toKeyAlias.ts\";\nexport { default as toStatement } from \"./converters/toStatement.ts\";\nexport { default as valueToNode } from \"./converters/valueToNode.ts\";\n\n// definitions\nexport * from \"./definitions/index.ts\";\n\n// modifications\nexport { default as appendToMemberExpression } from \"./modifications/appendToMemberExpression.ts\";\nexport { default as inherits } from \"./modifications/inherits.ts\";\nexport { default as prependToMemberExpression } from \"./modifications/prependToMemberExpression.ts\";\nexport {\n  default as removeProperties,\n  type Options as RemovePropertiesOptions,\n} from \"./modifications/removeProperties.ts\";\nexport { default as removePropertiesDeep } from \"./modifications/removePropertiesDeep.ts\";\nexport { default as removeTypeDuplicates } from \"./modifications/flow/removeTypeDuplicates.ts\";\n\n// retrievers\nexport { default as getAssignmentIdentifiers } from \"./retrievers/getAssignmentIdentifiers.ts\";\nexport { default as getBindingIdentifiers } from \"./retrievers/getBindingIdentifiers.ts\";\nexport { default as getOuterBindingIdentifiers } from \"./retrievers/getOuterBindingIdentifiers.ts\";\nexport { default as getFunctionName } from \"./retrievers/getFunctionName.ts\";\n\n// traverse\nexport { default as traverse } from \"./traverse/traverse.ts\";\nexport * from \"./traverse/traverse.ts\";\nexport { default as traverseFast } from \"./traverse/traverseFast.ts\";\n\n// utils\nexport { default as shallowEqual } from \"./utils/shallowEqual.ts\";\n\n// validators\nexport { default as is } from \"./validators/is.ts\";\nexport { default as isBinding } from \"./validators/isBinding.ts\";\nexport { default as isBlockScoped } from \"./validators/isBlockScoped.ts\";\nexport { default as isImmutable } from \"./validators/isImmutable.ts\";\nexport { default as isLet } from \"./validators/isLet.ts\";\nexport { default as isNode } from \"./validators/isNode.ts\";\nexport { default as isNodesEquivalent } from \"./validators/isNodesEquivalent.ts\";\nexport { default as isPlaceholderType } from \"./validators/isPlaceholderType.ts\";\nexport { default as isReferenced } from \"./validators/isReferenced.ts\";\nexport { default as isScope } from \"./validators/isScope.ts\";\nexport { default as isSpecifierDefault } from \"./validators/isSpecifierDefault.ts\";\nexport { default as isType } from \"./validators/isType.ts\";\nexport { default as isValidES3Identifier } from \"./validators/isValidES3Identifier.ts\";\nexport { default as isValidIdentifier } from \"./validators/isValidIdentifier.ts\";\nexport { default as isVar } from \"./validators/isVar.ts\";\nexport { default as matchesPattern } from \"./validators/matchesPattern.ts\";\nexport { default as validate } from \"./validators/validate.ts\";\nexport { default as buildMatchMemberExpression } from \"./validators/buildMatchMemberExpression.ts\";\nexport * from \"./validators/generated/index.ts\";\n\n// react\nexport const react = {\n  isReactComponent,\n  isCompatTag,\n  buildChildren,\n};\n\n// this is used by @babel/traverse to warn about deprecated visitors\nexport { default as __internal__deprecationWarning } from \"./utils/deprecationWarning.ts\";\n\nimport toSequenceExpression from \"./converters/toSequenceExpression.ts\" with { if: \"!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE\" };\nif (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n  // eslint-disable-next-line no-restricted-globals\n  exports.toSequenceExpression = toSequenceExpression;\n}\n\nif (!process.env.BABEL_8_BREAKING && process.env.BABEL_TYPES_8_BREAKING) {\n  console.warn(\n    \"BABEL_TYPES_8_BREAKING is not supported anymore. Use the latest Babel 8.0.0 pre-release instead!\",\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,iBAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AAGA,IAAAG,WAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AAAAK,MAAA,CAAAC,IAAA,CAAAF,MAAA,EAAAG,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAJ,MAAA,CAAAI,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAZ,MAAA,CAAAI,GAAA;IAAA;EAAA;AAAA;AAGA,IAAAS,kCAAA,GAAAjB,OAAA;AAEA,IAAAkB,oBAAA,GAAAlB,OAAA;AAEA,IAAAmB,kBAAA,GAAAnB,OAAA;AACA,IAAAoB,YAAA,GAAApB,OAAA;AAAAK,MAAA,CAAAC,IAAA,CAAAc,YAAA,EAAAb,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAY,YAAA,CAAAZ,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAI,YAAA,CAAAZ,GAAA;IAAA;EAAA;AAAA;AACA,IAAAa,OAAA,GAAArB,OAAA;AAAAK,MAAA,CAAAC,IAAA,CAAAe,OAAA,EAAAd,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAa,OAAA,CAAAb,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAK,OAAA,CAAAb,GAAA;IAAA;EAAA;AAAA;AAGA,IAAAc,UAAA,GAAAtB,OAAA;AACA,IAAAuB,MAAA,GAAAvB,OAAA;AACA,IAAAwB,UAAA,GAAAxB,OAAA;AACA,IAAAyB,oBAAA,GAAAzB,OAAA;AACA,IAAA0B,gBAAA,GAAA1B,OAAA;AAGA,IAAA2B,WAAA,GAAA3B,OAAA;AACA,IAAA4B,YAAA,GAAA5B,OAAA;AACA,IAAA6B,qBAAA,GAAA7B,OAAA;AACA,IAAA8B,uBAAA,GAAA9B,OAAA;AACA,IAAA+B,iBAAA,GAAA/B,OAAA;AACA,IAAAgC,wBAAA,GAAAhC,OAAA;AACA,IAAAiC,eAAA,GAAAjC,OAAA;AAGA,IAAAkC,OAAA,GAAAlC,OAAA;AAAAK,MAAA,CAAAC,IAAA,CAAA4B,OAAA,EAAA3B,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAA0B,OAAA,CAAA1B,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAkB,OAAA,CAAA1B,GAAA;IAAA;EAAA;AAAA;AACA,IAAA2B,OAAA,GAAAnC,OAAA;AAAAK,MAAA,CAAAC,IAAA,CAAA6B,OAAA,EAAA5B,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAA2B,OAAA,CAAA3B,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAmB,OAAA,CAAA3B,GAAA;IAAA;EAAA;AAAA;AAGA,IAAA4B,YAAA,GAAApC,OAAA;AACA,IAAAqC,wBAAA,GAAArC,OAAA;AACA,IAAAsC,QAAA,GAAAtC,OAAA;AACA,IAAAuC,cAAA,GAAAvC,OAAA;AACA,IAAAwC,aAAA,GAAAxC,OAAA;AACA,IAAAyC,aAAA,GAAAzC,OAAA;AACA,IAAA0C,WAAA,GAAA1C,OAAA;AACA,IAAA2C,YAAA,GAAA3C,OAAA;AACA,IAAA4C,YAAA,GAAA5C,OAAA;AAGA,IAAA6C,OAAA,GAAA7C,OAAA;AAAAK,MAAA,CAAAC,IAAA,CAAAuC,OAAA,EAAAtC,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAqC,OAAA,CAAArC,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAA6B,OAAA,CAAArC,GAAA;IAAA;EAAA;AAAA;AAGA,IAAAsC,yBAAA,GAAA9C,OAAA;AACA,IAAA+C,SAAA,GAAA/C,OAAA;AACA,IAAAgD,0BAAA,GAAAhD,OAAA;AACA,IAAAiD,iBAAA,GAAAjD,OAAA;AAIA,IAAAkD,qBAAA,GAAAlD,OAAA;AACA,IAAAmD,qBAAA,GAAAnD,OAAA;AAGA,IAAAoD,yBAAA,GAAApD,OAAA;AACA,IAAAqD,sBAAA,GAAArD,OAAA;AACA,IAAAsD,2BAAA,GAAAtD,OAAA;AACA,IAAAuD,gBAAA,GAAAvD,OAAA;AAGA,IAAAwD,SAAA,GAAAxD,OAAA;AACAK,MAAA,CAAAC,IAAA,CAAAkD,SAAA,EAAAjD,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAgD,SAAA,CAAAhD,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAwC,SAAA,CAAAhD,GAAA;IAAA;EAAA;AAAA;AACA,IAAAiD,aAAA,GAAAzD,OAAA;AAGA,IAAA0D,aAAA,GAAA1D,OAAA;AAGA,IAAA2D,GAAA,GAAA3D,OAAA;AACA,IAAA4D,UAAA,GAAA5D,OAAA;AACA,IAAA6D,cAAA,GAAA7D,OAAA;AACA,IAAA8D,YAAA,GAAA9D,OAAA;AACA,IAAA+D,MAAA,GAAA/D,OAAA;AACA,IAAAgE,OAAA,GAAAhE,OAAA;AACA,IAAAiE,kBAAA,GAAAjE,OAAA;AACA,IAAAkE,kBAAA,GAAAlE,OAAA;AACA,IAAAmE,aAAA,GAAAnE,OAAA;AACA,IAAAoE,QAAA,GAAApE,OAAA;AACA,IAAAqE,mBAAA,GAAArE,OAAA;AACA,IAAAsE,OAAA,GAAAtE,OAAA;AACA,IAAAuE,qBAAA,GAAAvE,OAAA;AACA,IAAAwE,kBAAA,GAAAxE,OAAA;AACA,IAAAyE,MAAA,GAAAzE,OAAA;AACA,IAAA0E,eAAA,GAAA1E,OAAA;AACA,IAAA2E,SAAA,GAAA3E,OAAA;AACA,IAAA4E,2BAAA,GAAA5E,OAAA;AACA,IAAA6E,OAAA,GAAA7E,OAAA;AAAAK,MAAA,CAAAC,IAAA,CAAAuE,OAAA,EAAAtE,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAqE,OAAA,CAAArE,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAA6D,OAAA,CAAArE,GAAA;IAAA;EAAA;AAAA;AAUA,IAAAsE,mBAAA,GAAA9E,OAAA;AAEA,IAAA+E,qBAAA,GAAA/E,OAAA;AATO,MAAMgF,KAAK,GAAAnE,OAAA,CAAAmE,KAAA,GAAG;EACnBC,gBAAgB,EAAhBA,yBAAgB;EAChBC,WAAW,EAAXA,oBAAW;EACXC,aAAa,EAAbA;AACF,CAAC;AAMgE;EAE/DtE,OAAO,CAACuE,oBAAoB,GAAGA,6BAAoB;AACrD;AAEA,IAAqCC,OAAO,CAACC,GAAG,CAACC,sBAAsB,EAAE;EACvEC,OAAO,CAACC,IAAI,CACV,kGACF,CAAC;AACH", "ignoreList": []}