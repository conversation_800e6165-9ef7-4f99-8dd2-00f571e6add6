{"version": 3, "file": "conversion.js", "sourceRoot": "", "sources": ["../../lib/conversion.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAAgC;AAChC,MAAM,KAAK,GAAG,IAAA,eAAW,EAAC,kBAAkB,CAAC,CAAC;AAE9C,kDAAuB;AACvB,oDAAyB;AACzB,gDAAwB;AAIxB;;;GAGG;AACH,SAAgB,IAAI,CAClB,KAAmE;IAEnE,IAAI,KAAK,KAAK,SAAS,EAAE;QACvB,OAAO,SAAS,CAAC;KAClB;SAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QACpC,OAAO,IAAI,eAAE,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;KAC1B;SAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QACpC,OAAO,IAAI,eAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;KACvC;SAAM,IAAI,OAAO,KAAK,IAAI,QAAQ,IAAI,eAAE,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QACrD,OAAO,IAAI,eAAE,CAAC,KAAK,CAAC,CAAC;KACtB;SAAM,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE;QACvB,OAAO,IAAI,eAAE,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,wCAAwC;QACxE,gFAAgF;QAChF,mDAAmD;QACnD,iFAAiF;KAClF;SAAM,IAAI,OAAO,KAAK,CAAC,MAAM,KAAK,UAAU,EAAE;QAC7C,OAAO,KAAK,CAAC,MAAM,CACjB,CAAC,GAAO,EAAE,IAAY,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EACjD,IAAI,eAAE,CAAC,CAAC,CAAC,CACV,CAAC;KACH;AACH,CAAC;AAtBD,oBAsBC;AAED,+DAA+D;AAC/D,MAAM;AACN,SAAgB,KAAK,CAAC,KAAU;IAC9B,OAAO,CACL,OAAO,KAAK,KAAK,QAAQ;QACzB,KAAK,KAAK,IAAI;QACd,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;QACjC,OAAO,KAAK,CAAC,CAAC,KAAK,QAAQ;QAC3B,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;QACtB,uDAAuD;QACvD,yDAAyD;QACzD,yDAAyD;QACzD,kEAAkE;QAClE,UAAU;QACV,CAAC,KAAK,CAAC,QAAQ,CAChB,CAAC;IACF,oEAAoE;IACpE,iEAAiE;IACjE,eAAe;AACjB,CAAC;AAjBD,sBAiBC;AAED;;;GAGG;AACH,SAAgB,UAAU,CAAC,KAAiB;IAC1C,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE;QACnB,oBAAoB;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC;KACpB;SAAM;QACL,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;aAClC,IAAI,CAAC,CAAC,CAAC;aACP,GAAG,EAAE,CAAC;KACV;AACH,CAAC;AATD,gCASC;AAED,SAAgB,QAAQ,CAAC,KAAS;IAChC,qEAAqE;IACrE,+DAA+D;IAC/D,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE;QACnB,CAAC,CAAC,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,qDAAqD;AACrG,CAAC;AAND,4BAMC;AAED,SAAgB,KAAK,CAAC,KAA2B;IAC/C,gFAAgF;IAChF,8DAA8D;IAC9D,OAAO,IAAI,gBAAG,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;AACnC,CAAC;AAJD,sBAIC;AAED;;;;;GAKG;AACH,SAAgB,WAAW,CACzB,KAA8C,EAC9C,YAAoB,CAAC,EACrB,WAAoB,KAAK;IAEzB,IACE,eAAE,CAAC,IAAI,CAAC,KAAK,CAAC;QACd,OAAO,KAAK,KAAK,QAAQ;QACzB,OAAO,KAAK,KAAK,QAAQ;QACzB,KAAK,CAAC,KAAK,CAAC,EACZ;QACA,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;KACxB;IAED,MAAM,GAAG,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;IAEhE,yDAAyD;IACzD,yDAAyD;IACzD,2DAA2D;IAC3D,2DAA2D;IAC3D,0CAA0C;IAC1C,+CAA+C;IAC/C,IAAI,KAAK,CAAC,MAAM,GAAG,SAAS,EAAE;QAC5B,IAAI,KAAK,GAAG,KAAK,CAAC;QAClB,KAAK,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC;QAElC,IAAI,QAAQ,EAAE;YACZ,4BAA4B;YAC5B,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;SAClB;aAAM;YACL,YAAY;YACZ,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;SAC5C;KACF;IAED,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IAE1B,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,CACvB,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,EAChD,EAAE,CACH,CAAC;IAEF,OAAO,KAAK,MAAM,EAAE,CAAC;AACvB,CAAC;AA3CD,kCA2CC;AAED,SAAgB,OAAO,CACrB,IAAyC,EACzC,SAAiB,CAAC;IAElB,6CAA6C;IAC7C,kCAAkC;IAClC,yCAAyC;IACzC,yEAAyE;IACzE,wBAAwB;IACxB,gFAAgF;IAChF,yBAAyB;IAEzB,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QAC5B,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;KAC1B;IAED,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QAC5B,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,sBAAsB;QAEtC,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;YACxB,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SACpB;QAED,IAAI,GAAG,KAAK,EAAE,EAAE;YACd,0EAA0E;YAC1E,gDAAgD;YAChD,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;SAC1B;QAED,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE;YACvB,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC;SACjB;QAED,IAAI,KAAK,GAAG,IAAI,UAAU,CACxB,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CACnD,CAAC;QAEF,IAAI,KAAK,CAAC,MAAM,GAAG,MAAM,EAAE;YACzB,IAAI,KAAK,GAAG,KAAK,CAAC;YAClB,KAAK,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;YAC/B,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;SACzC;QAED,OAAO,KAAK,CAAC;KACd;SAAM;QACL,qBAAqB;QACrB,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,IAAI,GAAG,IAAI,eAAE,CAAC,IAAI,CAAC,CAAC;SACrB;aAAM,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE;YACtB,gFAAgF;YAChF,mDAAmD;YACnD,IAAI,GAAG,IAAI,eAAE,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9B,iFAAiF;SAClF;QAED,oDAAoD;QACpD,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,UAAiB,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QAC5E,YAAY;KACb;AACH,CAAC;AA3DD,0BA2DC;AAED,SAAgB,aAAa,CAAC,KAAa;IACzC,KAAK,GAAG,cAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC3B,IAAI,KAAK,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;KAChC;IACD,OAAO,KAAK,CAAC;IACb,0EAA0E;IAC1E,yCAAyC;AAC3C,CAAC;AATD,sCASC;AAED,oCAAoC;AACpC,SAAgB,UAAU,CAAC,KAAU,EAAE,aAAqB;IAC1D,IAAI,QAAQ,GAAG,IAAI,gBAAG,CAAC,KAAK,CAAC,CAAC;IAC9B,QAAQ,CAAC,CAAC,IAAI,aAAa,CAAC;IAC5B,OAAO,QAAQ,CAAC;AAClB,CAAC;AAJD,gCAIC;AAED,qCAAqC;AACrC,SAAgB,YAAY,CAAC,KAAU,EAAE,aAAqB;IAC5D,IAAI,QAAQ,GAAG,IAAI,gBAAG,CAAC,KAAK,CAAC,CAAC;IAC9B,QAAQ,CAAC,CAAC,IAAI,aAAa,CAAC;IAC5B,OAAO,QAAQ,CAAC;AAClB,CAAC;AAJD,oCAIC;AAED,SAAgB,kBAAkB,CAAC,KAAU;IAC3C,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACnD,CAAC;AAFD,gDAEC;AAED,6DAA6D;AAC7D,iEAAiE;AACjE,oEAAoE;AACpE,mBAAmB;AACnB,SAAgB,SAAS,CACvB,MAA+B;IAE/B,QAAQ,MAAM,CAAC,IAAI,EAAE;QACnB,KAAK,OAAO;YACV,OAAO,MAAM,CAAC;QAChB,KAAK,OAAO;YACV,QAAQ,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE;gBACzB,KAAK,qBAAqB;oBACxB,aAAa;oBACb,OAAO;wBACL,IAAI,EAAkB,MAAM,CAAC,IAAI;wBACjC,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE;4BACL,SAAS,EAAE,IAAI;yBAChB;wBACD,eAAe,EAAE,EAAE;qBACpB,CAAC;gBACJ;oBACE,OAAO,MAAM,CAAC;aACjB;KACJ;AACH,CAAC;AAtBD,8BAsBC"}