{"version": 3, "file": "function.js", "sourceRoot": "", "sources": ["../../../lib/wrap/function.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAAgC;AAChC,MAAM,KAAK,GAAG,IAAA,eAAW,EAAC,qBAAqB,CAAC,CAAC;AAGjD,yCAA2C;AAC3C,qCAA6C;AAG7C,qDAAuC;AACvC,+CAAiC;AACjC,uDAAqD;AACrD,4DAAmC;AAEnC,uCAAyC;AACzC,mCAAqC;AAErC,MAAM,0BAA0B,GAI1B;IACJ,iCAAiC;IACjC,qBAAqB;IACrB,sCAAsC;IACtC,mBAAmB;CACpB,CAAC;AAEW,QAAA,qBAAqB,GAI5B,CAAC,0BAA0B,EAAE,GAAG,0BAA0B,CAAC,CAAC;AAElE,QAAQ,CAAC,CAAC,iCAAiC,CACzC,QAA2C,EAC3C,KAAc,EACd,WAAwB;IAExB,IAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC,KAAK,CAAC,EAAE;QACzC,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,iDAAiD,CAClD,CAAC;KACH;IACD,MAAM,cAAc,GAAG,CAAC,KAAK,CAAC,CAAC,IAAA,wBAAa,EAC1C,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,EACzC,KAAK,CAAC,OAAO,kCAER,WAAW,KACd,IAAI,EAAE,GAAG,WAAW,CAAC,IAAI,UAAU,EACnC,gBAAgB,EAAE,CAAC,KAErB,sBAAY,CACb,CAA+B,CAAC;IACjC,MAAM,OAAO,GAAG,cAAc,CAAC,KAAK,CAAC,SAAS,CAAC;IAC/C,MAAM,eAAe,GAAG,KAAK,CAAC,CAAC,IAAA,wBAAa,EAC1C,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,EAAE,EACjD,KAAK,CAAC,QAAQ,kCAET,WAAW,KACd,IAAI,EAAE,GAAG,WAAW,CAAC,IAAI,WAAW,EACpC,gBAAgB,EAAE,CAAC,KAErB,kBAAU,CACX,CAAC;IACF,MAAM,QAAQ,GAAG,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC;IAC7C,6DAA6D;IAC7D,OAAO;QACL,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE,OAAgB;QACtB,KAAK,EAAE;YACL,IAAI,EAAE,SAAkB;YACxB,QAAQ,EAAE;gBACR,IAAI,EAAE,SAAkB;gBACxB,OAAO;aACR;YACD,QAAQ;SACT;QACD,eAAe,EAAE,EAAE;KACpB,CAAC;AACJ,CAAC;AAED,QAAQ,CAAC,CAAC,sCAAsC,CAC9C,QAA2C,EAC3C,KAAc,EACd,WAAwB;IAExB,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;QACjC,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,gCAAgC,CACjC,CAAC;KACH;IACD,IACE,KAAK,CAAC,IAAI,CAAC,SAAS,KAAK,UAAU;QACnC,KAAK,CAAC,IAAI,CAAC,UAAU,KAAK,UAAU,EACpC;QACA,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,QAAQ,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,CACxC,CAAC;KACH;IACD,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;QAC1B,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,QAAQ,CAAC,kBAAkB,CAC5B,CAAC;KACH;IACD,MAAM,YAAY,GAAwC,KAAK,CAAC;IAChE,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC;IACpD,MAAM,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC7C,6CAA6C;IAC7C,OAAO;QACL,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE,OAAgB;QACtB,KAAK,EAAE;YACL,IAAI,EAAE,SAAkB;YACxB,QAAQ,EAAE;gBACR,IAAI,EAAE,SAAkB;gBACxB,OAAO;aACR;YACD,QAAQ;SACT;QACD,eAAe,EAAE,EAAE;KACpB,CAAC;AACJ,CAAC;AAED,QAAQ,CAAC,CAAC,qBAAqB,CAC7B,QAA2C,EAC3C,KAAc,EACd,WAAwB;IAExB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,wBAAwB,CACzB,CAAC;KACH;IACD,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;QAC9B,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,4DAA4D,CAC7D,CAAC;KACH;IACD,IACE,KAAK,CAAC,MAAM;QACZ,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,EACxD;QACA,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,QAAQ,CAAC,kBAAkB,CACzB,6CAA6C,EAC7C,QAAQ,CAAC,YAAY,GAAG,QAAQ,CAAC,aAAa,EAC9C,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CACvB,CACF,CAAC;KACH;IACD,IAAI,OAAO,GAAW,KAAK;SACxB,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC;SACvC,WAAW,EAAE,CAAC;IACjB,MAAM,QAAQ,GACZ,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;IAClE,oFAAoF;IACpF,8EAA8E;IAC9E,gCAAgC;IAChC,OAAO,GAAG,oBAAS,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAC/C,eAAe;IACf,OAAO;QACL,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE,OAAgB;QACtB,KAAK,EAAE;YACL,IAAI,EAAE,SAAkB;YACxB,QAAQ,EAAE;gBACR,IAAI,EAAE,SAAkB;gBACxB,OAAO;aACR;YACD,QAAQ;SACT;QACD,eAAe,EAAE,EAAE;KACpB,CAAC;AACJ,CAAC;AAED,QAAQ,CAAC,CAAC,0BAA0B,CAClC,QAA2C,EAC3C,KAAc,EACd,WAAwB;IAExB,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE;QAClC,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,iCAAiC,CAClC,CAAC;KACH;IACD,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE;QAC7B,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,QAAQ,CAAC,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC,CAC1C,CAAC;KACH;IACD,wDAAwD;IACxD,OAAO,KAAK,CAAC,CAAC,IAAA,wBAAa,EACzB,QAAQ,EACR,KAAK,CAAC,KAAK,kCACN,WAAW,KAAE,KAAK,EAAE,IAAI,KAC7B,0BAA0B,CAC3B,CAAC;AACJ,CAAC;AAED,QAAQ,CAAC,CAAC,mBAAmB,CAC3B,QAA2C,EAC3C,KAAc,EACd,WAAwB;IAExB,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,sIAAsI,CACvI,CAAC;AACJ,CAAC"}