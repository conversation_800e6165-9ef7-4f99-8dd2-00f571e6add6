const express = require('express');
const router = express.Router();

// import db
const db = require('../data/db');

  /*
landing page, returns all orders

GET /

curl http://localhost:8080/
  */
router.get('/', function(req, res) {
      var sqlOrder = "SELECT ? as type, logid, sender_email, recipient_email, order_id, coolerbox_id, \
      order_datetime, log_datetime, added_to_blockchain, transaction_hash, blockchain_url \
      from scb_order ORDER BY log_datetime DESC"
      var paramsOrder = ["order"]
      db.all(sqlOrder, paramsOrder, (errOrder, rowsOrder) => {
          if (errOrder) {
            console.log(errOrder.message)
                message = "Error querying scb_order"
                res.render('pages/index', {status: "error", data_order: null});
                return
          }
         
        console.log(`Rendering home page and ${rowsOrder.length} orders.`)
        res.render('pages/index', {
          status:"Success",
          data_order:rowsOrder,
          status: "success", 
        })
      });
  });

// tracking page
/* http://localhost:8080/tracking?order_id=12345 */
router.get('/tracking', function(req, res) {
  // process order_id from home page order links form
  if(req.query.order_id){
    const orderID = req.query.order_id;
    let message = "";

      var sqlOrder = "SELECT ? as type, logid, sender_email, recipient_email, order_id, coolerbox_id, \
      order_datetime, log_datetime, added_to_blockchain, transaction_hash, blockchain_url \
      from scb_order where order_id = ? \
      ORDER BY log_datetime ASC"
      var paramsOrder = ["order", orderID]
      db.all(sqlOrder, paramsOrder, (errOrder, rowsOrder) => {
          if (errOrder) {
            console.log(errOrder.message)
                message = "Error querying scb_order"
                res.render('pages/tracking', {status: "error", message: message, data_order: null});
                return
          }
          var sqlEvent = "SELECT ? as type, logid, sender_email, recipient_email, courier_email, coolerbox_id, \
          sensor_data, agent_type, agent_status, order_id, event_datetime, log_datetime, added_to_blockchain, \
          transaction_hash, blockchain_url from scb_event where order_id = ? \
          ORDER BY log_datetime ASC"
          var paramsEvent = ["event", orderID]
          db.all(sqlEvent, paramsEvent, (errEvent, rowsEvent) => {
              if (errEvent) {
                console.log(errEvent.message)
                message = "Error querying scb_event"
                res.render('pages/tracking', {status: "error", message: message, data_order: null});
                return
              }
              var sqlException = "SELECT ? as type, logid, recipient_email, agent, coolerbox_id, sensor_data, order_id, \
              exception_datetime, log_datetime,  added_to_blockchain, transaction_hash, blockchain_url from scb_exception \
                where order_id = ? ORDER BY log_datetime ASC"
              var paramsException = ["exception", orderID]
              db.all(sqlException, paramsException, (errException, rowsException) => {
                  if (errException) {
                    console.log(errException.message)
                    message = "Error querying scb_exception"
                    res.render('pages/tracking', {status: "error", message: message, data_order: null});
                    return
                  }
                  message = `Order ${orderID} blockchain links.`
              combinedRows = [...rowsOrder, ...rowsEvent, ...rowsException]
              console.log(`Tracking data for Order ${orderID}.`)

              res.render('pages/tracking', {
                status:"Success",
                data_order:rowsOrder,
                data_event:rowsEvent,
                data_exception:rowsException,
                status: "success", 
                message: message,
                package_id: orderID
              })
          });
        });
      });
  }else{
    res.render('pages/tracking', {data_order: null});
  }
});

router.get('/health', (req, res) => {
  res.status(200).send('OK')
})

  /*
get all order, event and exception data for an order. Related to api/v1/getorderdata

POST /tracking
Requirements: orderID

curl --header "Content-Type: application/json" \
  --request POST \
  --data '{"order_id":"12345"}'\
  http://localhost:8080/tracking
  */
  router.post('/tracking', (req, res)=>{
    const orderID = req.body.trackingID;
    let message = "";
  
    if(orderID){
      var sqlOrder = "SELECT ? as type, logid, sender_email, recipient_email, order_id, coolerbox_id, \
      order_datetime, log_datetime, added_to_blockchain, transaction_hash, blockchain_url \
      from scb_order where order_id = ? \
      ORDER BY log_datetime ASC"
      var paramsOrder = ["order", orderID]
      db.all(sqlOrder, paramsOrder, (errOrder, rowsOrder) => {
          if (errOrder) {
            console.log(errOrder.message)
                message = "Error querying scb_order"
                res.render('pages/tracking', {status: "error", message: message, data_order: null});
                return
          }
          var sqlEvent = "SELECT ? as type, logid, sender_email, recipient_email, courier_email, coolerbox_id, \
          sensor_data, agent_type, agent_status, order_id, event_datetime, log_datetime, added_to_blockchain, \
          transaction_hash, blockchain_url from scb_event where order_id = ? \
          ORDER BY log_datetime ASC"
          var paramsEvent = ["event", orderID]
          db.all(sqlEvent, paramsEvent, (errEvent, rowsEvent) => {
              if (errEvent) {
                console.log(errEvent.message)
                message = "Error querying scb_event"
                res.render('pages/tracking', {status: "error", message: message, data_order: null});
                return
              }
              var sqlException = "SELECT ? as type, logid, recipient_email, agent, coolerbox_id, sensor_data, order_id, \
              exception_datetime, log_datetime,  added_to_blockchain, transaction_hash, blockchain_url from scb_exception \
               where order_id = ? ORDER BY log_datetime ASC"
              var paramsException = ["exception", orderID]
              db.all(sqlException, paramsException, (errException, rowsException) => {
                  if (errException) {
                    console.log(errException.message)
                    message = "Error querying scb_exception"
                    res.render('pages/tracking', {status: "error", message: message, data_order: null});
                    return
                  }
                  message = `Order ${orderID} blockchain links.`
              combinedRows = [...rowsOrder, ...rowsEvent, ...rowsException]
              console.log(`Tracking data for Order ${orderID}.`)

              res.render('pages/tracking', {
                status:"Success",
                data_order:rowsOrder,
                data_event:rowsEvent,
                data_exception:rowsException,
                status: "success", 
                message: message,
                package_id: orderID
              })
          });
        });
      });
    }else{
      message = "Please supply order_id."
      console.log(`${message}`)
      res.render('pages/tracking', {status: "error", message: message, data_order: null});
    }
  });

module.exports = router;