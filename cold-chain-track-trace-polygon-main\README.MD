# Smart Cooler Box REST API

Smart Cooler Box REST API is a service that takes receives sensor readings (temparature and gps from a vaccine cooler box) from a Node-red instance and writes this data to the the blockchain (Polygon) in order to provide data integrity. This API service is developed using Express, NodeJS, and MongoDB.

![Smart Cooler Box Home Page](./public/images/scb.PNG?raw=true 'Smart Cooler Box Home Page')

![Smart Cooler Box Track Order](./public/images/scb_search.PNG?raw=true 'Smart Cooler Box Track Order')


![Smart Cooler Box Tracking Results](./public/images/scb_tracking.PNG?raw=true 'Smart Cooler Box Tracking Results')


## Installation (Development Environment)
The main files/folders  are:
- .env: Configuration file holding MongoDB connection string details.
- server.js: The main entry point for the Express server.
- routes/: Exposes the REST API endpoints and performs their business logic.

In order to run Smart Cooler Box REST API, an environment with the following is required:
- Node.js
- Express Framework
- Bootstrap

### Steps
1. Clone the smart-cooler-box repository.
```
<NAME_EMAIL>:web3/smart-cooler-box.git
```

2. Setup environment variables in `.env` file.
```
NODE_ENV=development
PORT=8080
INFURA_PROJECT_ID=<BLOCKCHAIN NODE KEY e.g. from Infura if you are using a hosted service to connect to blockchain>
CONTRACT_ADDRESS=<Your deployed smart contract address 0xAEa...>
ACCOUNT1_ADDRESS=<Your Account 1 address used to deploy smart contract and sign transactions to the blockchain 0xAEa...>
ACCOUNT1_MNEMONIC=<Your 12 phrase mnemonic for ACCOUNT 1>
BLOCKCHAIN_NODE_RPC_URL=http://127.0.0.1:8545
BLOCKCHAIN_EXPLORER_URL=http://localhost:8545/
```

3. Install node dependencies.

```
npm install
```

4. Start the web server (Express) and navigate to http://localhost:8080/ in your browser.

```
npm run dev
```

## Deployment (Development Server)
### Steps
1. ssh into the server via PuTTY or your terminal.

2. Clone the smart-cooler-box repository.
```
$ <NAME_EMAIL>:web3/smart-cooler-box.git
```

3. Setup environment variables in `.env` file.

- Create .env file
```
$ touch .env
$ nano .env
```

- Update with the following contents, save and exit.
```
NODE_ENV=development
PORT=8080
MNEMONIC=<Your 12 phrase mnemonic>
INFURA_PROJECT_ID=<Your Infura project id>
CONTRACT_ADDRESS=<Your deployed smart contract address 0xAEa...>
ACCOUNT1_ADDRESS=<Your Account 1 address used to deploy smart contract and sign transactions to the blockchain 0xAEa...>
ACCOUNT1_MNEMONIC=<Your 12 phrase mnemonic for ACCOUNT 1>
BLOCKCHAIN_NODE_RPC_URL=<Your RPC URL from Ganache or EVM-compatible Testnet>
BLOCKCHAIN_EXPLORER_URL=<Your BLOCKCHAIN EXPLORER URL from Ganache or EVM-compatible Testnet e.g. https://mumbai.polygonscan.com/tx/>
```

- Check file contents
```
$ cat .env
``` 

4. Install node dependencies.

```
$ npm install
```

5. Start the web server (Express) using pm2 and navigate to https://DEV-SERVER-IP-ADDRESS:8080/ in your browser. Log output to scb_logfile_dev.log

```
$ pm2 start server.js –-name smart-cooler-box 
```

6. Other pm2 commands

- To restart the server
```
$ pm2 restart smart-cooler-box
```

- To reload the server
```
$ pm2 reload smart-cooler-box
```

- To stop the server
```
$ pm2 stop smart-cooler-box
```

- To delete from pm2 processes
```
$ pm2 delete smart-cooler-box
```

- To list pm2 processes
```
$ pm2 [list|ls|status]
```

- To display logs
```
$ pm2 logs smart-cooler-box
$ pm2 logs smart-cooler-box > scb_logfile_dev.log 2>&1
```

- To display terminal dashboard
```
$ pm2 monit
```

- pm2 docs
```
https://pm2.keymetrics.io/docs/usage/quick-start/
```

## Smart Contract
### Steps
1. Create smart contract (not necessary to run this as I have already run it and commited SmartCoolerBox.sol).
```
$ truffle create contract SmartCoolerBox
```

2. After making changes to SmartCoolerBox.sol, compile it.
```
$ truffle compile
```

3. Deploy to development network or testnet.

- You must first update truffle-config.js with development network settings (Ganache) and testnet settings (Polygon Mumbai)

- Deploy to Ganache.
```
$ truffle migrate --network development
```

- Deploy to Polygon Testnet. You will need to top up the account you are deploying from with MATIC token from a MATIC faucet (e.g. https://faucet.polygon.technology/).
``` 
$ truffle migrate --network mumbai
```

4. Interact with deployed smart contract.
- Truffle console connects to an existing blockchain:
``` 
$ truffle console
```

- Begin by establishing both the deployed SmartCoolerBox contract instance and the accounts created by Ganache:
``` 
truffle(development)> let instance = await SmartCoolerBox.deployed()
truffle(development)> let accounts = await web3.eth.getAccounts()
```

- Check number of SmartCoolerBox orders recorded on the blockchain
```
truffle(development)> instance.getNumberOfOrderRecords()
```

- Add an order record to the 
```
truffle(development)> instance.addOrderRecord("<EMAIL>","<EMAIL>","12345", "6789", "2023-08-07T16:36:00")
```

## API v1 Documentation

This section describes the operations/endpoints available in the Smart Cooler Box REST API service. 

Summary below:
| Operation                                                   | HTTP Method | Endpoint                   |
| :---------------------------------------------------------- | :---------- | :--------------------------|
| Create an order                                             |  POST       | /api/v1/createorder/       |
| Log supply chain event                                      |  POST       | /api/v1/logevent/          |
| Log supply chain exception event                            |  POST       | /api/v1/logexception/      |
| Get all blockchain URLs for an order                        |  GET        | /api/v1/getblockchainlinks/|
| Get Blockchain Explorer URL for an order's genesis record   |  GET        | /api/v1/getgenesisurl/     |
| Get Blockchain Explorer URL for an order's event record     |  GET        | /api/v1/geteventurl/       |
| Get Blockchain Explorer URL for an order's exception record |  GET        | /api/v1/getexceptionurl/   |

The datetime format for the API is YYYY-MM-DDTHH:mm:ss which is the basic ISO 8601 format for mixed date-time. Description below:
- YYYY is the 4-digit year
- MM is the 2-digit month (zero-padded)
- DD is the 2-digit day (zero-padded)
- T is a constant to indicate that the time follows
- HH is the 2-digit hour (zero-padded, 24 hour format)
- mm is the 2-digit minute (zero-padded)
- ss is the 2-digit second (zero-padded)

### Create an order (i.e. a package)
This endpoint is used to create the initial record for an order. An order represents a package being shipped inside a cooler box. The `coolerBoxID` is the ID of the sensor.

POST /createorder 

Requirements: senderEmail, recipientEmail, orderID, coolerBoxID, orderDateTime

```
curl --header "Content-Type: application/json" \
  --request POST \
  --data '{"sender_email":"<EMAIL>", "recipient_email":"<EMAIL>",
  "order_id":"12345","coolerbox_id":"1", "order_datetime":"2023-07-25T13:15:23"}'\
  http://localhost:8080/api/v1/createorder
```

Output - 
{"status":"Success","message":"Order 12345 from sender@gmail.<NAME_EMAIL> logged to blockchain."}

### Log supply chain event
This endpoint is used to log sensor values associated with an order. This should be called immediately after an order is created, and any time custody of the cooler box changes. The custodia is represented by `agentType` and the status is recorded from their perspective.

POST /logevent  
 
Requirements: senderEmail, recipientEmail, courierCompanyName, courierDeliveryPerson, coolerBoxID, sensorData (a dictionary of values), agentType, agentStatus, orderID, custodian, eventDateTime(YYYY-MM-DDTHH:mm:ss)

```
curl --header "Content-Type: application/json" \
  --request POST \
  --data '{"sender_email":"<EMAIL>", "recipient_email":"<EMAIL>",
  "courier_email":"<EMAIL>", "coolerbox_id":"1",
  "sensor_data":"{temperature:35,latitude:45.317,longitude:16.606,gps_timestamp:2023-07-24h16:34:23}", "agent_type":"sender",
  "agent_status":"collected","order_id":"12345", "event_datetime":"2023-07-25T13:15:23"}'\
  http://localhost:8080/api/v1/logevent
```

Output -
{"status":"Success","message":"Supply chain event for order 12345 with custodian courier, and status collected(sender) logged to blockchain."}

### Log supply chain exception event
This endpoint is used to log exception sensor values associated with an and should be called immediately after a breach of normal readings is detected.

POST /logexception  

Requirements: recipientEmail, agent (a dictionary of values), coolerBoxID, sensorData (a dictionary of values), orderID, exceptionDatetime (YYYY-MM-DDTHH:mm:ss)
 
```
curl --header "Content-Type: application/json" \
  --request POST \
  --data '{"recipient_email":"<EMAIL>","agent":"{type:Sender,status:packed,email:<EMAIL>}", 
  "coolerbox_id":"1","sensor_data":"{temperature:35,latitude:45.317,longitude:16.606,gps_timestamp:2023-07-24h16:34:23}",
  "order_id":"12345","exception_datetime":"2023-07-25T13:15:23"}'\
  http://localhost:8080/api/v1/logexception
```

Output -
{"status":"Success","message":"Supply chain exception for order 12345 with status {type:Sender,status:packed,email:<EMAIL>} logged to blockchain."}

### Get all blockchain URLs for an order
This endpoint is used to return all of the blockchain URLs for a given order ID, together with the event datetimes.

POST /getblockchainlinks  

Requirements:  orderID
 
```
curl curl --header "Content-Type: application/json"   --request GET   --data '{"order_id":"12345"}'  http://localhost:8080/api/v1/getblockchainlinks
```

Output -
{"status":"Success","message":"Order 12345 blockchain links.","data":[{"type":"order","logid":"742a0b08-2091-4815-9fdb-3804bdf0dc86","order_datetime":"2023-07-25T13:15:23","blockchain_url":"https://mumbai.polygonscan.com/tx/0xb257ade39d352489b4b9be843f37a1f2202ed03a530e6a4e50b869feae19ceea"},{"type":"event","logid":"d11f1abd-144b-401b-bea9-e60c7ca49d74","event_datetime":"2023-07-25T13:15:23","blockchain_url":"https://mumbai.polygonscan.com/tx/0xc0e749224c4231d9a278216147321fa38eda9d57c6405ac1b927d12904e46e4a"},{"type":"exception","logid":"d11f1abd-144b-401b-bea9-e60c7ca49d74","order_id":"12345","event_datetime":"2023-07-25T13:15:23","blockchain_url":"https://mumbai.polygonscan.com/tx/0xc0e749224c4231d9a278216147321fa38eda9d57c6405ac1b927d12904e46e4a"}]}


### Get Blockchain Explorer URL for an order's genesis record
This endpoint is used to return a blockchain explorer URL for an order's initial (genesis) entry to the blockchain.

GET /orderblockchainurl

Requirements: orderID

```
curl --header "Content-Type: application/json"   --request GET   --data '{"order_id":"12345"}'  http://localhost:8080/api/v1/orderblockchainurl
```

Output -
{"status":"Success","message":"Order 12345 blockchain URL.","data":[{"logid":"742a0b08-2091-4815-9fdb-3804bdf0dc86","order_id":"12345","order_datetime":"2023-07-25T13:15:23","blockchain_url":"https://mumbai.polygonscan.com/tx/0xb257ade39d352489b4b9be843f37a1f2202ed03a530e6a4e50b869feae19ceea"}]}


### Get all data - order, event and exception for an order ID
This endpoint is used to return all the order, event and exception data for a given order ID.

GET /getorderdata

Requirements: orderID

```
curl --header "Content-Type: application/json"   --request GET   --data '{"order_id":"12345"}'  http://localhost:8080/api/v1/getorderdata
```

Output -
{"status":"Success","message":"Order 12345 blockchain links.","data_order":[{"type":"order","logid":"742a0b08-2091-4815-9fdb-3804bdf0dc86","sender_email":"<EMAIL>","recipient_email":"<EMAIL>","order_id":"12345","coolerbox_id":"1","order_datetime":"2023-07-25T13:15:23","log_datetime":"2023-08-15T16:30:42","added_to_blockchain":"True","transaction_hash":"0xb257ade39d352489b4b9be843f37a1f2202ed03a530e6a4e50b869feae19ceea","blockchain_url":"https://mumbai.polygonscan.com/tx/0xb257ade39d352489b4b9be843f37a1f2202ed03a530e6a4e50b869feae19ceea"}],"data_event":[{"type":"event","logid":"d11f1abd-144b-401b-bea9-e60c7ca49d74","sender_email":"<EMAIL>","recipient_email":"<EMAIL>","courier_email":"<EMAIL>","coolerbox_id":"1","sensor_data":"{temperature:35,latitude:45.317,longitude:16.606,gps_timestamp:2023-07-24h16:34:23}","agent_type":"sender","agent_status":"collected","order_id":"12345","event_datetime":"2023-07-25T13:15:23","log_datetime":"2023-08-15T16:30:08","added_to_blockchain":"True","transaction_hash":"0xc0e749224c4231d9a278216147321fa38eda9d57c6405ac1b927d12904e46e4a","blockchain_url":"https://mumbai.polygonscan.com/tx/0xc0e749224c4231d9a278216147321fa38eda9d57c6405ac1b927d12904e46e4a"}],"data_exception":[{"type":"exception","logid":"d11f1abd-144b-401b-bea9-e60c7ca49d74","sender_email":"<EMAIL>","recipient_email":"<EMAIL>","courier_email":"<EMAIL>","coolerbox_id":"1","sensor_data":"{temperature:35,latitude:45.317,longitude:16.606,gps_timestamp:2023-07-24h16:34:23}","agent_type":"sender","agent_status":"collected","order_id":"12345","event_datetime":"2023-07-25T13:15:23","log_datetime":"2023-08-15T16:30:08","added_to_blockchain":"True","transaction_hash":"0xc0e749224c4231d9a278216147321fa38eda9d57c6405ac1b927d12904e46e4a","blockchain_url":"https://mumbai.polygonscan.com/tx/0xc0e749224c4231d9a278216147321fa38eda9d57c6405ac1b927d12904e46e4a"}]}

### Get Blockchain Explorer URL for an order's event record
This endpoint is used to return a blockchain explorer URL for an event, given a orderID and transactionID

GET /geteventurl

Requirements: orderID, transactionID

```
curl -k http://localhost:8080/api/v1/geteventurl?order_id=12345&transactionID=34KSDF343
```

Output -
{"status":"Success", "message":"Blockchain explorer URL for order with ID 12345 and event with transactionID=34KSDF343 is https://explorer.testnet.com/tx/34KSDF343", "rawURL": https://explorer.testnet.com/tx/34KSDF343}

### Get Blockchain Explorer URL for an order's exception record
This endpoint is used to return a blockchain explorer URL for an exception, given a orderID and transactionID

GET /getexceptionurl

Requirements: orderID, transactionID

```
curl -k http://localhost:8080/api/v1/getexceptionurl?order_id=12345&transactionID=34KSDF343
```

Output -
{"status":"Success", "message":"Blockchain explorer URL for order with ID 12345 and exception with transactionID=34KSDF343 is https://explorer.testnet.com/tx/34KSDF343", "rawURL": https://explorer.testnet.com/tx/34KSDF343}
