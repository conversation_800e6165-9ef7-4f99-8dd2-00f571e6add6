{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../lib/basic/allocate/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAAgC;AAChC,MAAM,KAAK,GAAG,IAAA,eAAW,EAAC,sBAAsB,CAAC,CAAC;AAElD,qDAAgD;AAChD,+CAA0C;AAC1C,qDAAgD;AAEhD,wBAAwB;AACxB,SAAgB,UAAU,CACxB,QAA2B,EAC3B,gBAAyC;IAEzC,QAAQ,QAAQ,CAAC,SAAS,EAAE;QAC1B,KAAK,MAAM;YACT,OAAO,CAAC,CAAC;QACX,KAAK,SAAS,CAAC;QACf,KAAK,UAAU;YACb,OAAO,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC;QAChC,KAAK,KAAK,CAAC;QACX,KAAK,MAAM,CAAC;QACZ,KAAK,OAAO,CAAC;QACb,KAAK,QAAQ;YACX,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAC;QAC3B,KAAK,UAAU;YACb,QAAQ,QAAQ,CAAC,UAAU,EAAE;gBAC3B,KAAK,UAAU;oBACb,OAAO,GAAG,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC;gBAC/B,KAAK,UAAU;oBACb,OAAO,GAAG,CAAC,KAAK,CAAC,YAAY,GAAG,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC;aAC3D;QACH,KAAK,OAAO,EAAE,oCAAoC;YAChD,OAAsC,QAAS,CAAC,MAAM,CAAC;QACzD,KAAK,MAAM,CAAC,CAAC;YACX,MAAM,UAAU,GAA0B,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACxE,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;gBACtC,MAAM,IAAI,MAAM,CAAC,2BAA2B,CAC1C,QAAQ,CAAC,EAAE,EACX,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAClC,CAAC;aACH;YACD,MAAM,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC;YAC5C,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;SAC5C;QACD,KAAK,sBAAsB,CAAC,CAAC;YAC3B,MAAM,UAAU,GAA0C,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACxF,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE;gBAC7C,MAAM,IAAI,MAAM,CAAC,2BAA2B,CAC1C,QAAQ,CAAC,EAAE,EACX,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAClC,CAAC;aACH;YACD,MAAM,EAAE,cAAc,EAAE,GAAG,UAAU,CAAC;YACtC,OAAO,UAAU,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC;SACrD;KACF;AACH,CAAC;AA/CD,gCA+CC"}