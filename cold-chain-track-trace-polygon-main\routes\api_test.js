const shortid = require('shortid')
const express = require('express');
const moment = require("moment");
const Web3 = require('web3');
const contract = require('@truffle/contract');
const Provider = require('@truffle/hdwallet-provider')
const fs = require('fs');

// load environment variables from a .env file into process.env
require('dotenv').config() 

const router = express.Router();
const dateTimeFormat = 'YYYY-MM-DDThh:mm:ss'

//smart contract scaffolding
const scbJSONFile = './build/contracts/SmartCoolerBox.json';
const scbParsedJSONFile= JSON.parse(fs.readFileSync(scbJSONFile));
const scbABI = scbParsedJSONFile.abi;
const scbContractAddress = process.env.CONTRACT_ADDRESS
const scbAccountAddress = process.env.ACCOUNT1_ADDRESS
const scbAccountMnemonic = process.env.ACCOUNT1_MNEMONIC;
const blockchainNodeRPC = process.env.BLOCKCHAIN_NODE_RPC_URL;

/* The provider helps web3 dapps to talk or interact with the blockchain. 
These Providers take JSON-RPC requests and return the response. 
Providers can be HTTP/web socket or IPC based:
- HTTP (http provider is a node which communicates through http protocols.),
- Web Socket (Websocket Provider uses web socket communication protocol over TCP that enables bidirectional communication between client and server.), 
- IPC (The IPC provider is running a local node. It gives the most secure connection.) 
Simply, Web3 Provider is a server/website running geth or parity node which talks to an EVM-compatible network. */
const provider = new Provider(scbAccountMnemonic, blockchainNodeRPC);
const web3 = new Web3(provider);

//create contract object
const scbContract= new web3.eth.Contract(scbABI, scbContractAddress);

// query blockchain
router.get('/api/test/queryblockchain', async function(req, res) {
  console.log(`GET Test API queryblockchain...`);
  try{
    var numOrderRecords = await scbContract.methods.getNumberOfOrderRecords().call()
    var numOrderRecords2 = await scbContract.methods.getNumberOfOrderRecords().call()

    console.log("numOrderRecords", numOrderRecords);
    console.log("numOrderRecords2", numOrderRecords2);

    //insert into DB
    apiMessage = `numOrderRecords from SCB blockchain is ${numOrderRecords}.`
    res.json({"status":"Success", "message":apiMessage});
  }
  catch(err){
    res.status(500).json({"status":"Failed", "message":"Error reading from blockchain."});
} 
});

/*
create an order and write to blockchain. orderDateTime is current time.

curl --header "Content-Type: application/json" \
  --request POST \
  --data '{"sender_email":"<EMAIL>", "recipient_email":"<EMAIL>",
  "order_id":"12345","coolerbox_id":"1"}'\
  http://localhost:8080/api/test/writeblockchain
  */
router.post('/api/test/writeblockchain', async function(req, res) {
  console.log(`POST Test API writeblockchain...`);
  const senderEmail = req.body.sender_email;
  const recipientEmail = req.body.recipient_email;
  const orderID = req.body.order_id;
  const coolerBoxID = req.body.coolerbox_id;
  const orderDateTime = moment().format(dateTimeFormat);

  try{
    var orderRecordReceipt = await scbContract.methods.addOrderRecord(senderEmail, recipientEmail, orderID, coolerBoxID, orderDateTime).send({ from: scbAccountAddress })
    console.log("orderRecordReceipt", orderRecordReceipt);

    //insert into DB
    apiMessage = `orderRecord added to SCB blockchain. Transaction hash is ${orderRecordReceipt.transactionHash}.`
    res.json({"status":"Success", "message":apiMessage});
  }
  catch(err){
    res.status(500).json({"status":"Failed", "message":"Error reading from blockchain."});
} 
});


// about page
router.get('/api/test/test', function(req, res) {
  console.log(`GET Test API...`);
  res.json({"status":"Success", "message":"Test API..."});
});

// create record
router.post('/api/test/register', (req,res)=>{
    let userEmail = req.body.email;
    let userId = shortid.generate();
    if(userEmail){
      res.json({"status":"Success", "email": userEmail, "id":userId});
    }else{
        res.status(400).json({"status":"Failed", "reason":"wrong input"});
    }
});

/*
read record using URL params using req.query. req.query is used to retrieve values for URL parameters.
 e.g. http://localhost:8080/api/test/user?email=<EMAIL>&id=12345
 */
router.get('/api/test/user', (req,res)=>{
  if(req.query.id && req.query.email){
    const userEmail = req.query.email;
    const userId = req.query.id;
    res.json({"status":"Success", "email": userEmail, "id": userId});
  }else{
      res.status(400).json({"status":"Failed", "reason":"wrong input"});
  }
});

/* 
read record using URL params using req.params. req.params is used to retrieve values from routes without params.
e.g. read record e.g. http://localhost:8080/api/test/user/<EMAIL>/12345 
*/
router.get('/api/test/user/:email/:id', (req,res)=>{
  if(req.params.id && req.params.email){
    let userEmail = req.params.email;
    let userId = req.params.id;
    res.json({"status":"Success", "email": userEmail, "id": userId});
  }else{
      res.status(400).json({"status":"Failed", "reason":"wrong input"});
  }
});

/*
create an order v2

curl -X GET "http://localhost:8080/api/v1/createorder?sender_email=<EMAIL>&recipient_email=<EMAIL>&order_id=12345&coolerbox_id=1"

curl --request GET "http://localhost:8080/api/v1/createorder?sender_email=<EMAIL>&recipient_email=<EMAIL>&order_id=12345&coolerbox_id=1"

*/
router.get('/api/test/createorder', (req,res)=>{
  const senderEmail = req.query.sender_email;
  const recipientEmail = req.query.recipient_email;
  const orderID = req.query.order_id;
  const coolerBoxID = req.query.coolerbox_id;

  const logDateTime = moment().format(dateTimeFormat);
  let apiMessage = "";

  if(senderEmail && recipientEmail && orderID && coolerBoxID){
    apiMessage = `Order ${orderID} from ${senderEmail} to ${recipientEmail} logged to blockchain.`
    res.json({"status":"Success", "message":apiMessage});
  }else{
    apiMessage = "Please supply sender_email, recipient_email, order_id and coolerbox_id."
    res.status(400).json({"status":"Failed", "message":apiMessage});
  }
});

module.exports = router;