{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../lib/wrap/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAAgC;AAChC,MAAM,KAAK,GAAG,IAAA,eAAW,EAAC,YAAY,CAAC,CAAC;AAMxC,yCAAoD;AACpD,qCAKkB;AAMhB,wGAVA,gCAAuB,OAUA;AACvB,0GAVA,kCAAyB,OAUA;AACzB,kGAVA,0BAAiB,OAUA;AACjB,qGAVA,6BAAoB,OAUA;AARtB,2CAAyB;AACzB,iCAA8B;AASrB,qFATA,WAAI,OASA;AACb,0CAAwB;AACxB,uDAAuC;AAEvC,QAAe,CAAC,CAAC,YAAY,CAC3B,KAAyC,EACzC,MAAiB,EACjB,WAAwB;IAExB,oDAAoD;IACpD,MAAM,YAAY,GAA2B;QAC3C,SAAS,EAAE,OAAO;QAClB,WAAW,EAAE,KAAK;KACnB,CAAC;IACF,KAAK,CAAC,mBAAmB,CAAC,CAAC;IAC3B,MAAM,eAAe,GAA6B,CAChD,KAAK,CAAC,CAAC,IAAA,WAAI,EAAC,YAAY,EAAE,MAAM,EAAE,WAAW,CAAC,CAC/C,CAAC;IACF,OAAO,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAsB,KAAK,CAAC,CAAC;AAC9E,CAAC;AAfD,oCAeC;AAED,sBAAsB;AACtB,QAAe,CAAC,CAAC,aAAa,CAC5B,MAAc,EACd,MAAiB,EACjB,cAA8B;IAE9B,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,IAAI,CAAC,CAAC;IAC9E,OAAO,oBAAoB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAC/C,CAAC;AAPD,sCAOC;AAED,SAAS,oBAAoB,CAC3B,MAAc,EACd,OAA8B;IAE9B,IACE,OAAO,CAAC,MAAM,GAAG,CAAC;QAClB,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,KAAK,SAAS,EACxD;QACA,iBAAiB;QACjB,MAAM,gBAAgB,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB;QAChE,MAAM,OAAO,GAAgC,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAE;aACtE,KAAK,CAAC;QACT,OAAO;YACL,MAAM;YACN,SAAS,EAAE,gBAAgB;YAC3B,OAAO;SACR,CAAC;KACH;SAAM;QACL,YAAY;QACZ,OAAO;YACL,MAAM;YACN,SAAS,EAAE,OAAO;YAClB,OAAO,EAAE,EAAE;SACZ,CAAC;KACH;AACH,CAAC;AAED,qEAAqE;AACrE,QAAQ,CAAC,CAAC,gBAAgB,CACxB,MAAc,EACd,MAAiB,EACjB,EAAE,gBAAgB,EAAE,YAAY,EAAE,SAAS,EAAE,cAAc,EAAkB,EAC7E,QAAiB,KAAK;IAEtB,KAAK,CAAC,qBAAqB,CAAC,CAAC;IAC7B,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE;QAC1C,iBAAiB;QACjB,KAAK,CAAC,YAAY,CAAC,CAAC;QACpB,OAAO,KAAK,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE;YAChD,gBAAgB;YAChB,kBAAkB,EAAE,IAAI;YACxB,KAAK;YACL,IAAI,EAAE,aAAa;YACnB,SAAS;YACT,cAAc;SACf,CAAC,CAAC;KACJ;SAAM,IAAI,YAAY,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;QACrE,cAAc;QACd,KAAK,CAAC,SAAS,CAAC,CAAC;QACjB,MAAM,iBAAiB,GAAG;YACxB,GAAG,MAAM,CAAC,MAAM;YAChB,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,EAAE,SAAS,EAAE,SAAkB,EAAE,EAAE;SAC/D,CAAC;QACF,OAAO,KAAK,CAAC,CAAC,YAAY,CAAC,iBAAiB,EAAE,MAAM,EAAE;YACpD,gBAAgB;YAChB,kBAAkB,EAAE,IAAI;YACxB,KAAK;YACL,IAAI,EAAE,aAAa;YACnB,SAAS;YACT,cAAc;SACf,CAAC,CAAC;KACJ;SAAM;QACL,qBAAqB;QACrB,MAAM,SAAS,GAAG,YAAY;YAC5B,CAAC,CAAC,QAAQ,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,gCAAgC;YAClE,CAAC,CAAC,EAAE,CAAC;QACP,MAAM,IAAI,0BAAiB,CACzB,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,CAAC,MAAM,EAAE,EAClD,MAAM,EACN,aAAa,EACb,CAAC,EACD,2CAA2C,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,SAAS,SAAS,MAAM,CAAC,MAAM,GAAG,CACrG,CAAC;KACH;AACH,CAAC;AAED,QAAe,CAAC,CAAC,cAAc,CAC7B,OAAiB,EACjB,MAAiB,EACjB,EAAE,gBAAgB,EAAE,YAAY,EAAE,SAAS,EAAE,cAAc,EAAkB;IAE7E,qEAAqE;IACrE,uEAAuE;IACvE,yEAAyE;IACzE,wDAAwD;IACxD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;QACxB,iEAAiE;QACjE,6DAA6D;QAC7D,wDAAwD;QACxD,OAAO,KAAK,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE;YAC9C,gBAAgB;YAChB,YAAY;YACZ,SAAS;YACT,cAAc;SACf,CAAC,CAAC;KACJ;IACD,4FAA4F;IAC5F,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CACpC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CACjD,CAAC;IACF,+DAA+D;IAC/D,IAAI,0BAA0B,GAAa,EAAE,CAAC;IAC9C,IAAI,eAAe,GAAmB,EAAE,CAAC;IACzC,IAAI,YAAY,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;QACrC,yDAAyD;QACzD,uEAAuE;QACvE,8EAA8E;QAC9E,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC5C,IAAI,iBAAiB,GAAY,IAAI,CAAC;QACtC,IAAI;YACF,MAAM,cAAc,GAA+B,CACjD,KAAK,CAAC,CAAC,IAAA,WAAI,EAAC,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,SAAS,EAAE;gBAC/C,IAAI,EAAE,WAAW;gBACjB,KAAK,EAAE,IAAI;gBACX,kBAAkB,EAAE,IAAI;gBACxB,gBAAgB;gBAChB,SAAS;gBACT,cAAc;aACf,CAAC,CACH,CAAC;YACF,eAAe,GAAG,cAAc,CAAC,KAAK,CAAC;SACxC;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,KAAK,YAAY,0BAAiB,EAAE;gBACtC,iBAAiB,GAAG,KAAK,CAAC;aAC3B;iBAAM;gBACL,MAAM,KAAK,CAAC,CAAC,2BAA2B;aACzC;SACF;QACD,IAAI,iBAAiB,EAAE;YACrB,0BAA0B,GAAG,OAAO,CAAC,MAAM,CACzC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,GAAG,CAAC,CACrD,CAAC;SACH;KACF;IACD,KAAK,CAAC,qBAAqB,EAAE,eAAe,CAAC,CAAC;IAC9C,KAAK,CAAC,gCAAgC,EAAE,0BAA0B,CAAC,CAAC;IACpE,6CAA6C;IAC7C,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,IAAI,0BAA0B,CAAC,MAAM,KAAK,CAAC,EAAE;QAC3E,sEAAsE;QACtE,gDAAgD;QAChD,KAAK,CAAC,kCAAkC,CAAC,CAAC;QAC1C,MAAM,MAAM,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;QAClC,OAAO;YACL,MAAM;YACN,SAAS,EAAE,KAAK,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE;gBACpD,gBAAgB;gBAChB,KAAK,EAAE,IAAI;gBACX,IAAI,EAAE,aAAa;gBACnB,SAAS;gBACT,cAAc;aACf,CAAC;YACF,OAAO,EAAE,EAAE;SACZ,CAAC;KACH;SAAM,IACL,0BAA0B,CAAC,MAAM,KAAK,CAAC;QACvC,eAAe,CAAC,MAAM,KAAK,CAAC,EAC5B;QACA,kFAAkF;QAClF,wDAAwD;QACxD,KAAK,CAAC,mCAAmC,CAAC,CAAC;QAC3C,MAAM,MAAM,GAAG,0BAA0B,CAAC,CAAC,CAAC,CAAC;QAC7C,OAAO;YACL,MAAM;YACN,SAAS,EAAE,KAAK,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE;gBACpD,gBAAgB;gBAChB,KAAK,EAAE,IAAI;gBACX,IAAI,EAAE,aAAa;gBACnB,SAAS;gBACT,cAAc;aACf,CAAC;YACF,OAAO,EAAE,eAAe;SACzB,CAAC;KACH;SAAM,IACL,eAAe,CAAC,MAAM,KAAK,CAAC;QAC5B,0BAA0B,CAAC,MAAM,KAAK,CAAC,EACvC;QACA,KAAK,CAAC,kBAAkB,CAAC,CAAC;QAC1B,kBAAkB;QAClB,MAAM,IAAI,gCAAuB,CAAC,OAAO,EAAE,MAAM,EAAE,gBAAgB,CAAC,CAAC;KACtE;IACD,4EAA4E;IAC5E,4EAA4E;IAC5E,OAAO;IACP,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACxC,IAAI,WAAW,GAAiB,EAAE,CAAC;IACnC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;QAC5B,IAAI,OAA8B,CAAC;QACnC,IAAI;YACF,gDAAgD;YAChD,6DAA6D;YAC7D,OAAO,GAAG,KAAK,CAAC,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,EAAE;gBAChD,gBAAgB;gBAChB,YAAY;gBACZ,SAAS;gBACT,cAAc;aACf,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,mCAAmC;YACnC,KAAK,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YAC5B,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;YAC5B,SAAS;SACV;QACD,sEAAsE;QACtE,oBAAoB;QACpB,KAAK,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;QAC5B,WAAW,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC;KAC/D;IACD,iDAAiD;IACjD,KAAK,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;IACtC,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAC5C,WAAW,CAAC,KAAK,CACf,oBAAoB,CAAC,EAAE,CACrB,CAAC,IAAA,iCAAsB,EACrB,oBAAoB,CAAC,SAAS,EAC9B,UAAU,CAAC,SAAS,EACpB,cAAc,EACd,gBAAgB,CACjB;QACD,kEAAkE;QAClE,4BAA4B;QAC5B,iDAAiD;QACjD,gDAAgD;QAChD,uBAAuB;QACvB,IAAA,iCAAsB,EACpB,UAAU,CAAC,SAAS,EACpB,oBAAoB,CAAC,SAAS,EAC9B,cAAc,EACd,gBAAgB,CACjB,CACJ,CACF,CAAC;IACF,KAAK,CAAC,2BAA2B,EAAE,WAAW,CAAC,CAAC;IAChD,QAAQ,WAAW,CAAC,MAAM,EAAE;QAC1B,KAAK,CAAC;YACJ,sBAAsB;YACtB,MAAM,IAAI,gCAAuB,CAAC,OAAO,EAAE,MAAM,EAAE,gBAAgB,CAAC,CAAC;QACvE,KAAK,CAAC;YACJ,oEAAoE;YACpE,kBAAkB;YAClB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YACtD,OAAO,oBAAoB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAC/C;YACE,oCAAoC;YACpC,MAAM,IAAI,kCAAyB,CAAC,WAAW,CAAC,CAAC;KACpD;AACH,CAAC;AAzKD,wCAyKC"}