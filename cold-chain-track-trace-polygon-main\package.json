{"name": "smart-cooler-box", "version": "1.0.0", "description": "Smart Cooler Box API web app for interfacing with the blockchain", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon server.js", "start": "node server.js"}, "author": "<PERSON>", "license": "ISC", "dependencies": {"@truffle/contract": "^4.6.28", "@truffle/hdwallet-provider": "^2.1.13", "dotenv": "^16.3.1", "ejs": "^3.1.9", "express": "^4.18.2", "moment": "^2.29.4", "morgan": "^1.10.0", "shortid": "^2.2.16", "sqlite3": "^5.1.6", "uuid": "^9.0.0", "web3": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.1"}}