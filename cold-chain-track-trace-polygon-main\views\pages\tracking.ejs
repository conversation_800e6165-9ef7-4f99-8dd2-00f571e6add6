<!DOCTYPE html>
<html lang="en">
<head>
  <%- include('../partials/head'); %>
</head>
<body class="container">

<header>
  <%- include('../partials/header'); %>
</header>

<main>
  <div class="jumbotron">
    <h1>Smart Cooler Box Tracking</h1>
    <p>Track your shipment using the package ID</p>
  </div>
  <div class="container">
    <% if  (!data_order) { %>
    <div class="row h-100 justify-content-center align-items-center">    
      <div class="col-sm-12 text-center">
        <h3>Need the status of your shipment or proof of delivery? Enter your tracking number or reference number below. </p>
      </div>
      <div class="col-sm-6 text-center">
        <form action="/tracking" method="POST">
          <div class="form-group row">
            <div class="input-group">
                <input type="text" class="form-control" name="trackingID" placeholder="tracking number...">
            </div>
          </div>
          <div class="form-group row">
            <button class="form-control btn btn-sm btn-secondary" type="submit">Track</button>
          </div>
            
        </form>
      </div>
    </div>
    <% } else { %>
    <hr>
    <div class="d-flex justify-content-between">
      <div><h3>Package Timeline for Order <%= package_id %></h3></div>
      <div> <a href="/tracking">Track another package</a></div>
    </div>

    <% data_order.forEach(function(item,index){ %>
   <!--    <%= item.type %> <%= item.logid %> <%= item.sender_email %> <%= item.recipient_email %> <%= item.order_id %>
      <%= item.coolerbox_id %> <%= item.order_datetime %> <%= item.log_datetime %> <%= item.added_to_blockchain %>
      <%= item.transaction_hash %>  <%= item.blockchain_url %>   -->

      <!-- timeline item 1 -->
    <div class="row small">
      <!-- timeline item 1 left dot -->
      <div class="col-auto text-center flex-column d-none d-sm-flex border-success">
        <div class="row h-50">
          <div class="col">&nbsp;</div>
          <div class="col">&nbsp;</div>
        </div>
        <h5 class="m-2">
          <span class="badge badge-pill bg-light border border-success">&nbsp;</span>
        </h5>
        <div class="row h-50">
          <div class="col border-right border-success">&nbsp;</div>
          <div class="col">&nbsp;</div>
        </div>
      </div>
      <!-- timeline item 1 event content -->
      <div class="col py-2">
        <div class="card border-success">
          <div class="card-body">
            <div class="float-right text-muted"><%= item.order_datetime %></div>
            <h4 class="card-title">Order Placed</h4>
            <p class="card-text">Order ID - <%= item.order_id %></p>
            <p class="card-text">Cooler Box ID - <%= item.coolerbox_id %></p>
            <p class="card-text">Sender - <%= item.sender_email %></p>
            <p class="card-text">Receiver - <%= item.recipient_email %></p>
            <p class="card-text">Status: Ready for collection</p>
            <p class="card-text">Tracked on blockchain <a href= <%= item.blockchain_url %>><%= item.transaction_hash %></a></p>
          </div>
        </div>
      </div>
    </div>
    <!--/row-->

  <% }) %>

  <% if  (data_event) { %>
    <% data_event.forEach(function(item_event,index_event){ %>
      <!--    <%= item_event.type %> <%= item_event.logid %> <%= item_event.sender_email %> 
        <%= item_event.recipient_email %> <%= item_event.courier_email %> <%= item_event.coolerbox_id %> 
        <%= item_event.sensor_data %> <%= item_event.agent_type %> <%= item_event.agent_status %> 
        <%= item_event.order_id %> <%= item_event.event_datetime %> <%= item_event.log_datetime %> 
        <%= item_event.added_to_blockchain %> <%= item_event.transaction_hash %>  
        <%= item_event.blockchain_url %>   -->

     <!-- timeline item 1 -->
     <div class="row small">
      <!-- timeline item 1 left dot -->
      <div class="col-auto text-center flex-column d-none d-sm-flex border-success">
        <div class="row h-50">
          <div class="col">&nbsp;</div>
          <div class="col">&nbsp;</div>
        </div>
        <h5 class="m-2">
          <span class="badge badge-pill bg-light border border-success">&nbsp;</span>
        </h5>
        <div class="row h-50">
          <div class="col border-right border-success">&nbsp;</div>
          <div class="col">&nbsp;</div>
        </div>
      </div>
      <!-- timeline item 1 event content -->
      <div class="col py-2">
        <div class="card border-success">
          <div class="card-body">
            <div class="float-right text-muted"><%= item_event.event_datetime %></div>
            <h4 class="card-title">Event - <%= item_event.agent_status %> </h4>
            <p class="card-text">Order ID - <%= item_event.order_id %></p>
            <p class="card-text">Cooler Box ID - <%= item_event.coolerbox_id %></p>
            <p class="card-text">Sender - <%= item_event.sender_email %></p>
            <p class="card-text">Receiver - <%= item_event.recipient_email %></p>
            <p class="card-text">Courier - <%= item_event.courier_email %></p>
            <p class="card-text">Custodian - <%= item_event.agent_type %></p>
            <p class="card-text">Sensor readings - <%= item_event.sensor_data %></p>
            <p class="card-text">Status: <%= item_event.agent_status %></p>
            <p class="card-text">Tracked on blockchain <a href= <%= item_event.blockchain_url %>><%= item_event.transaction_hash %></a></p>
          </div>
        </div>
      </div>
    </div>
    <!--/row-->
    <% }) %>
  <% } %>

  <% if  (data_exception) { %>
    <% data_exception.forEach(function(item_exception,index_exception){ %>
         <!-- <%= item_exception.type %> <%= item_exception.logid %> <%= item_exception.recipient_email %> 
        <%= item_exception.agent %> <%= item_exception.coolerbox_id %> <%= item_exception.sensor_data %> 
        <%= item_exception.order_id %> <%= item_exception.exception_datetime %> <%= item_exception.log_datetime %> 
        <%= item_exception.added_to_blockchain %> <%= item_exception.transaction_hash %>  
        <%= item_exception.blockchain_url %>   -->

     <!-- timeline item 1 -->
     <div class="row small">
      <!-- timeline item 1 left dot -->
      <div class="col-auto text-center flex-column d-none d-sm-flex border-danger">
        <div class="row h-50">
          <div class="col">&nbsp;</div>
          <div class="col">&nbsp;</div>
        </div>
        <h5 class="m-2">
          <span class="badge badge-pill bg-light border border-danger">&nbsp;</span>
        </h5>
        <div class="row h-50">
          <div class="col border-right border-danger">&nbsp;</div>
          <div class="col">&nbsp;</div>
        </div>
      </div>
      <!-- timeline item 1 event content -->
      <div class="col py-2">
        <div class="card border-danger">
          <div class="card-body">
            <div class="float-right text-muted"><%= item_exception.exception_datetime %></div>
            <h4 class="card-title">Exception</h4>
            <p class="card-text">Order ID - <%= item_exception.order_id %></p>
            <p class="card-text">Cooler Box ID - <%= item_exception.coolerbox_id %></p>
            <p class="card-text">Receiver - <%= item_exception.recipient_email %></p>
            <p class="card-text">Custodian - <%= item_exception.agent %></p>
            <p class="card-text">Sensor readings - <%= item_exception.sensor_data %></p>
            <p class="card-text">Tracked on blockchain <a href= <%= item_exception.blockchain_url %>><%= item_exception.transaction_hash %></a></p>
          </div>
        </div>
      </div>
    </div>
    <!--/row-->
    <% }) %>
  <% } %>
    
  </div>
  <!--container-->
  <% } %>
  <div class="row h-50">
    <div class="col">&nbsp;</div>
    <div class="col">&nbsp;</div>
  </div>
</main>

<footer>
  <%- include('../partials/footer'); %>
</footer>

</body>
</html>