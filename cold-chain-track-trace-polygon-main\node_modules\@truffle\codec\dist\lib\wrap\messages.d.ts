import type BN from "bn.js";
import type { IntegerOrEnumType, DecimalType } from "./types";
import * as Format from "../format";
export declare function wrongArrayLengthMessage(expected: number | BN, got: number): string;
export declare const errorResultMessage: string;
export declare const notAStringMessage: string;
export declare const nonIntegerMessage: string;
export declare const nonNumericMessage: string;
export declare const nonSafeMessage: string;
export declare const badEnumMessage: string;
export declare const outOfRangeMessage: string;
export declare const outOfRangeEnumMessage: string;
export declare const checksumFailedMessage: string;
export declare const invalidUtf16Message: string;
export declare const looseModeOnlyMessage: string;
export declare const negativeBytesMessage: string;
export declare function wrappedTypeMessage(dataType: Format.Types.Type): string;
export declare function specifiedTypeMessage(dataType: string): string;
export declare function overlongMessage(expected: number, got: number): string;
export declare function tooPreciseMessage(expected: number, got: number): string;
export declare function notABytestringMessage(what: string): string;
export declare function wrongLengthMessage(what: string, expected: number, got: number): string;
export declare function unrecognizedNumberMessage(dataType: IntegerOrEnumType | DecimalType): string;
