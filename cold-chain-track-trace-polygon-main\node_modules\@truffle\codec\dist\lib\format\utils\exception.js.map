{"version": 3, "file": "exception.js", "sourceRoot": "", "sources": ["../../../../lib/format/utils/exception.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;GAIG;AACH,kDAAgC;AAChC,MAAM,KAAK,GAAG,IAAA,eAAW,EAAC,8BAA8B,CAAC,CAAC;AAE1D,kDAAuD;AACvD,0DAAqD;AAGrD,sCAAsC;AACtC,6CAA6C;AAC7C,0CAA0C;AAC1C,SAAgB,OAAO,CAAC,KAAqC;IAC3D,QAAQ,KAAK,CAAC,IAAI,EAAE;QAClB,KAAK,8BAA8B;YACjC,IAAI,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,KAAK,CAAC,IAAI,CAAC;gBAC3D,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,oBAAoB,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ;gBAC7D,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;YACxB,OAAO,WAAW,KAAK,CAAC,IAAI,CAAC,SAAS,SAAS,QAAQ,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;QACnF,KAAK,0BAA0B;YAC7B,OAAO,6BAA6B,QAAQ,CAAC,SAAS,CACpD,KAAK,CAAC,UAAU,CACjB,EAAE,CAAC;QACN,KAAK,sBAAsB;YACzB,OAAO,8BAA8B,CAAC;QACxC,KAAK,gBAAgB;YACnB,OAAO,kCAAkC,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,EAAE,EAAE,CAAC;QACvE,KAAK,gBAAgB;YACnB,OAAO,cAAc,KAAK,CAAC,MAAM,eAAe,KAAK,CAAC,QAAQ,gBAAgB,KAAK,CAAC,KAAK,EAAE,CAAC;QAC9F,KAAK,kBAAkB;YACrB,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE;gBACtB,OAAO,cACL,KAAK,CAAC,KAAK,CAAC,MACd,yCACE,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KACnB,OAAO,mBAAmB,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;aACrD;iBAAM;gBACL,OAAO,iCACL,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KACnB,OAAO,mBAAmB,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAC/C,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,KACjB,OAAO,mBAAmB,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;aACnD;QACH,KAAK,yBAAyB;YAC5B,OAAO,2BAA2B,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,oCAAoC;QACjG,KAAK,sBAAsB;YACzB,OAAO,4BAA4B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,oCAAoC;KAC3F;AACH,CAAC;AApCD,0BAoCC;AAED,SAAS,mBAAmB,CAAC,IAAkB;IAC7C,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE;QACrD,oBAAoB;QACpB,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1E,OAAO,CACL,SAAS;YACT,QAAQ;YACR,MAAM;YACN,WAAW;YACX,IAAI;YACJ,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC;YAC9B,MAAM;YACN,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CACvB,CAAC;KACH;SAAM,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE;QAClC,MAAM,mBAAmB,GAAG,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3D,OAAO,IAAI,CAAC,QAAQ;YAClB,CAAC,CAAC,SAAS,GAAG,mBAAmB,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;YAChE,CAAC,CAAC,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;KAClD;SAAM;QACL,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;KAC/B;AACH,CAAC;AAED,2EAA2E;AAC3E,mDAAmD;AACnD,yEAAyE;AACzE,SAAS,kBAAkB,CAAC,KAAoC;IAI9D,QAAQ,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE;QAC5B,KAAK,MAAM;YACT,OAAO;gBACL,IAAI,EAAE,MAAM;gBACZ,KAAK,EAA4B,KAAM,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;aAC9D,CAAC;QACJ,KAAK,KAAK;YACR,OAAO;gBACL,IAAI,EAAE,KAAK;gBACX,KAAK,EAA2B,KAAM,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE;aAC7D,CAAC;QACJ,KAAK,OAAO;YACV,OAAO;gBACL,IAAI,EAAE,YAAY,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE;gBACrC,KAAK,EAA6B,KAAM,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE;aAChE,CAAC;QACJ,KAAK,QAAQ;YACX,OAAO;gBACL,IAAI,EAAE,aAAa,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE;gBACtC,KAAK,EAA8B,KAAM,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE;aACjE,CAAC;QACJ,KAAK,MAAM;YACT,oEAAoE;YACpE,OAAO;gBACL,IAAI,EAAE,MAAM;gBACZ,KAAK,EAA4B,KAAM,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,EAAE;aACnE,CAAC;QACJ,KAAK,OAAO;YACV,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE;gBACvB,KAAK,QAAQ;oBACX,OAAO;wBACL,IAAI,EAAE,SAAS;wBACf,KAAK,EAA6B,KAAM,CAAC,KAAK,CAAC,KAAK;qBACrD,CAAC;gBACJ,KAAK,SAAS;oBACZ,OAAO;wBACL,IAAI,EAAE,OAAO;wBACb,KAAK,EAA6B,KAAM,CAAC,KAAK,CAAC,KAAK;qBACrD,CAAC;aACL;QACH,KAAK,SAAS;YACZ,OAAO;gBACL,IAAI,EAAE,SAAS;gBACf,KAAK,EAA+B,KAAM,CAAC,KAAK,CAAC,SAAS;aAC3D,CAAC;QACJ,KAAK,QAAQ;YACX,IAAI,YAAY,GAAyD,CACvE,KAAK,CACN,CAAC;YACF,QAAQ,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE;gBAC/B,KAAK,OAAO;oBACV,OAAO;wBACL,IAAI,EAAE,QAAQ;wBACd,KAAK,EAAE,YAAY,CAAC,KAAK,CAAC,QAAQ;qBACnC,CAAC;gBACJ,KAAK,WAAW;oBACd,OAAO;wBACL,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE,YAAY,CAAC,KAAK,CAAC,KAAK;qBAChC,CAAC;aACL;QACH,sCAAsC;KACvC;AACH,CAAC"}