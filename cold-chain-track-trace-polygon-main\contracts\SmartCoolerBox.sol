// SPDX-License-Identifier: MIT 
pragma solidity >=0.4.22 <0.9.0;


/// <AUTHOR> CSIR Future Production
/// @title Smart Cooler Box Contract
contract SmartCoolerBox {
  constructor() {
  }

  // define the struct OrderRecord
  struct OrderRecord {
    string sender_email;
    string recipient_email;
    string order_id;
    string coolerbox_id;
    string order_datetime;
  }

  // define the struct EventRecord
  struct EventRecord {
    string email_data; // sender_email, recipient_email & courier_email;
    string order_data; //coolerbox_id; order_id
    string sensor_data;
    string agent_data; //agent_type & agent_status
    string event_datetime;
  }

  // define the struct ExceptionRecord
  struct ExceptionRecord {
    string recipient_email;
    string agent;
    string order_data; // order_id, coolerbox_id
    string sensor_data;
    string exception_datetime;
  }

  // event fired on submission of a OrderRecord entry.
  event registeredOrderRecordEvent (
    string _order_id,
    string _coolerbox_id,
    string _order_datetime,
    uint _submissionBlockNumber
  );

  // event fired on submission of a EventRecord entry.
  event registeredEventRecordEvent (
    string _order_data,
    string _event_datetime,
    uint _submissionBlockNumber
  );

  // event fired on submission of a ExceptionRecord entry.
  event registeredExceptionRecordEvent (
    string _order_data,
    string _exception_datetime,
    uint _submissionBlockNumber
  );
  
  // define the array of order records 
  OrderRecord[] public order_records;

  // define the array of event records 
  EventRecord[] public event_records;

  // define the array of exception records 
  ExceptionRecord[] public exception_records;

  /// Add order record
  /// @param _sender_email senderof package
  /// @param _recipient_email receiver of package
  /// @param _order_id order ID for the package
  /// @param _coolerbox_id ID of cooler box used to transport package
  /// @param _order_datetime datetime the order is placed - YYYY-MM-DDThh:mm:ss 
  /// @dev Creates order record in `OrderRecord` array
  function addOrderRecord(string calldata _sender_email, string calldata _recipient_email,
    string calldata _order_id, string calldata _coolerbox_id, string calldata _order_datetime) external returns(uint){

    uint256 submissionBlockNumber = block.number; //block is a global variable

    // get an instance of a OrderRecord using the input variables and push into the array of order_records
    order_records.push(OrderRecord(_sender_email, _recipient_email, _order_id, _coolerbox_id, _order_datetime));

    // trigger event for OrderRecord registration
    emit registeredOrderRecordEvent(_order_id, _coolerbox_id, _order_datetime, submissionBlockNumber);
    
    // return the order position in the order_records array
    return  order_records.length-1;
  }

  /// Add event record
  /// @param _email_data dict of sender email, courier email and receiver email
  /// @param _order_data dict of order_id and coolerbox_id for the package
  /// @param _sensor_data dict of sensor readings e.g. temperature and gps
  /// @param _agent_data dict of agent (i.e. custodian) and package status
  /// @param _event_datetime datetime the event occurs - YYYY-MM-DDThh:mm:ss 
  /// @dev Creates order record in `OrderRecord` array
  function addEventRecord(string calldata _email_data, string calldata _order_data,
    string calldata _sensor_data, string calldata _agent_data, string calldata _event_datetime) external returns(uint){

    uint256 submissionBlockNumber = block.number; //block is a global variable

    // get an instance of a EventRecord using the input variables and push into the array of event_records
    event_records.push(EventRecord(_email_data, _order_data, _sensor_data, _agent_data, _event_datetime));

    // trigger event for EventRecord registration
    emit registeredEventRecordEvent(_order_data, _event_datetime, submissionBlockNumber);
    
    // return the order position in the event_records array
    return  event_records.length-1;
  }

  /// Add exception record
  /// @param _recipient_email receiver of the package
  /// @param _agent current custodian of the package
  /// @param _order_data dict of order_id and coolerbox_id for the package
  /// @param _sensor_data dict of sensor readings e.g. temperature and gps
  /// @param _exception_datetime datetime the exception (deviation of sensor reading value from norm range) occurs - YYYY-MM-DDThh:mm:ss 
  /// @dev Creates order record in `ExceptionRecord` array
  function addExceptionRecord(string calldata _recipient_email, string calldata _agent,
    string calldata _order_data, string calldata _sensor_data, string calldata _exception_datetime) external returns(uint){

    uint256 submissionBlockNumber = block.number; //block is a global variable

    // get an instance of a ExceptionRecord using the input variables and push into the array of exception_records
    exception_records.push(ExceptionRecord(_recipient_email, _agent, _order_data, _sensor_data, _exception_datetime));

    // trigger event for ExceptionRecord registration
    emit registeredExceptionRecordEvent(_order_data, _exception_datetime, submissionBlockNumber);
    
    // return the order position in the exception_records array
    return  exception_records.length-1;
  }
  
  /// Return the number of order records
  /// @dev returns the number of elements in the order_records array
  /// @return the number of order records
  function getNumberOfOrderRecords() external view returns(uint) {
    // return the length of the order_records array
    return order_records.length;
  }

  /// Return the number of event records
  /// @dev returns the number of elements in the event_records array
  /// @return the number of event records
  function getNumberOfEventRecords() external view returns(uint) {
    // return the length of the event_records array
    return event_records.length;
  }

  /// Return the number of exception records
  /// @dev returns the number of elements in the exception_records array
  /// @return the number of exception records
  function getNumberOfExceptionRecord() external view returns(uint) {
    // return the length of the exception_records array
    return exception_records.length;
  }
}
