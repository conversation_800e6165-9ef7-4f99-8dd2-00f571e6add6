const shortid = require('shortid')
const express = require('express');
const moment = require("moment");
const Web3 = require('web3');
const contract = require('@truffle/contract');
const Provider = require('@truffle/hdwallet-provider')
const fs = require('fs');
const {v4 : uuidv4} = require('uuid')

// load environment variables from a .env file into process.env
require('dotenv').config() 

// import db
const db = require('../data/db');

const router = express.Router();
const dateTimeFormat = 'YYYY-MM-DDTHH:mm:ss'
//TODO Convert all dates and store in UTC (Greenwich Mean Time) timezone. Display in UTC+2


//smart contract scaffolding
const scbJSONFile = './build/contracts/SmartCoolerBox.json';
const scbParsedJSONFile= JSON.parse(fs.readFileSync(scbJSONFile));
const scbABI = scbParsedJSONFile.abi;
const scbContractAddress = process.env.CONTRACT_ADDRESS
const scbAccountAddress = process.env.ACCOUNT1_ADDRESS
const scbAccountMnemonic = process.env.ACCOUNT1_MNEMONIC;
const blockchainNodeRPC = process.env.BLOCKCHAIN_NODE_RPC_URL;
const blockchainExplorerURL = process.env.BLOCKCHAIN_EXPLORER_URL

/* The provider helps web3 dapps to talk or interact with the blockchain. 
These Providers take JSON-RPC requests and return the response. 
Providers can be HTTP/web socket or IPC based:
- HTTP (http provider is a node which communicates through http protocols.),
- Web Socket (Websocket Provider uses web socket communication protocol over TCP that enables bidirectional communication between client and server.), 
- IPC (The IPC provider is running a local node. It gives the most secure connection.) 
Simply, Web3 Provider is a server/website running geth or parity node which talks to an EVM-compatible network. 
*/
const provider = new Provider(scbAccountMnemonic, blockchainNodeRPC);
const web3 = new Web3(provider);

//create contract object
const scbContract= new web3.eth.Contract(scbABI, scbContractAddress);

/*
create an order

curl --header "Content-Type: application/json" \
  --request POST \
  --data '{"sender_email":"<EMAIL>", "recipient_email":"<EMAIL>",
  "order_id":"12345","coolerbox_id":"1", "order_datetime":"2023-07-25T13:15:23"}'\
  http://localhost:8080/api/v1/createorder
  */
router.post('/api/v1/createorder', async (req, res)=>{
  const senderEmail = req.body.sender_email;
  const recipientEmail = req.body.recipient_email;
  const orderID = req.body.order_id;
  const coolerBoxID = req.body.coolerbox_id;
  const orderDateTime = req.body.order_datetime;

  const logID = uuidv4();
  const logDateTime = moment().format(dateTimeFormat);
  let apiMessage = "";

  if(senderEmail && recipientEmail && orderID && coolerBoxID && orderDateTime){
      try{
        var orderTransactionDetails = await scbContract.methods.addOrderRecord(senderEmail, recipientEmail, orderID, coolerBoxID, orderDateTime).send({ from: scbAccountAddress })
        
        let transactionHash = orderTransactionDetails.transactionHash
        let transactionURL = `${blockchainExplorerURL}${transactionHash}`
        apiMessage = `Order ${orderID} from ${senderEmail} to ${recipientEmail} logged to blockchain. Transaction hash is ${transactionHash}.`

        console.log(apiMessage);
        console.log(orderTransactionDetails);
      
        //insert into DB
        const sql_insert = `INSERT INTO scb_order (logid, sender_email, recipient_email, order_id, coolerbox_id, order_datetime, log_datetime, added_to_blockchain,
          transaction_hash, blockchain_url) VALUES
          ('${logID}','${senderEmail}', '${recipientEmail}', '${orderID}', '${coolerBoxID}', '${orderDateTime}',
           '${logDateTime}', 'True', '${transactionHash}', '${transactionURL}');`;

          db.run(sql_insert, (err) => {
            if (err) {
              console.error(err.message);
              res.status(500).json({"status":"Failed", "message":"Database error occured."});
              return
            }
            console.log(`Successful creation of order record ${orderID} with transaction hash ${transactionHash} and logID ${logID} in DB.`);
            res.json({"status":"Success", "message":apiMessage});
          });
      }
      catch(err){
        res.status(500).json({"status":"Failed", "message":"Error logging to blockchain."});
    } 
  }else{
    apiMessage = "Please supply sender_email, recipient_email, order_id and coolerbox_id."
    res.status(400).json({"status":"Failed", "message":apiMessage});
  }
});


/*
log supply chain event

curl --header "Content-Type: application/json" \
  --request POST \
  --data '{"sender_email":"<EMAIL>", "recipient_email":"<EMAIL>",
  "courier_email":"<EMAIL>", "coolerbox_id":"1",
  "sensor_data":"{temperature:35,latitude:45.317,longitude:16.606,gps_timestamp:2023-07-24h16:34:23}", "agent_type":"sender",
  "agent_status":"collected","order_id":"12345", "event_datetime":"2023-07-25T13:15:23"}'\
  http://localhost:8080/api/v1/logevent
  */
router.post('/api/v1/logevent', async (req, res)=>{
  const senderEmail = req.body.sender_email;
  const recipientEmail = req.body.recipient_email;
  const courierEmail = req.body.courier_email;
  const coolerBoxID = req.body.coolerbox_id;
  const sensorData = req.body.sensor_data;
  const agentType = req.body.agent_type;
  const agentStatus = req.body.agent_status;
  const orderID = req.body.order_id;
  const eventDateTime = req.body.event_datetime;

  const logID = uuidv4();
  const logDateTime = moment().format(dateTimeFormat);
  let apiMessage = "";

  if(senderEmail && recipientEmail && courierEmail && coolerBoxID && 
    sensorData && agentType && agentStatus && orderID && eventDateTime)
    {
      try{
        let emailData = `{sender_email: ${senderEmail}, reciever_email: ${recipientEmail}, 
        courier_email: ${courierEmail}}`;
        console.log(`emailData ${emailData}`);

        let orderData = `{order_id: ${orderID}, coolerbox_id: ${coolerBoxID}}`;
        console.log(`orderData ${orderData}`);

        let agentData =`{agent_type: ${agentType}, agent_status: ${agentStatus}}`; 
        console.log(`agentData ${agentData}`);
      
        var eventTransactionDetails = await scbContract.methods.addEventRecord(emailData, orderData, 
          sensorData, agentData, eventDateTime).send({ from: scbAccountAddress })
        
        let transactionHash = eventTransactionDetails.transactionHash
        let transactionURL = `${blockchainExplorerURL}${transactionHash}`
        apiMessage = `Event with time ${eventDateTime} for order ${orderID} from ${senderEmail} to 
          ${recipientEmail} and status ${agentStatus}(${agentType}) logged to blockchain. Transaction hash is 
          ${transactionHash}.`

        console.log(apiMessage);
        console.log(eventTransactionDetails);
      
        //insert into DB
        const sql_insert = `INSERT INTO scb_event (logid, sender_email, recipient_email, courier_email, coolerbox_id, 
          sensor_data, agent_type, agent_status, order_id, event_datetime, log_datetime, added_to_blockchain, 
          transaction_hash, blockchain_url) VALUES
          ('${logID}','${senderEmail}', '${recipientEmail}', '${courierEmail}', '${coolerBoxID}', '${sensorData}',
          '${agentType}', '${agentStatus}', '${orderID}', '${eventDateTime}', '${logDateTime}', 'True', 
          '${transactionHash}', '${transactionURL}');`;
          
          console.log(sql_insert);
          db.run(sql_insert, (err) => {
            if (err) {
              console.error(err.message);
              res.status(500).json({"status":"Failed", "message":"Database error occured."});
              return
            }
            console.log(`Successful creation of event record for ${orderID} with event time ${eventDateTime}, 
            and status ${agentStatus}(${agentType}), with transaction hash ${transactionHash} and logID ${logID} in DB.`);
            res.json({"status":"Success", "message":apiMessage});
          });
      }
    catch(err){
      res.status(500).json({"status":"Failed", "message":"Error logging to blockchain."});
    }
  }
  else{
    apiMessage = "Please supply sender_email, recipient_email, courier_email, coolerbox_id,\
    sensor_data, agent_type, agent_status, order_id & event_datetime."
    res.status(400).json({"status":"Failed", "message":apiMessage});
  }
});

/*
log supply chain exception

curl --header "Content-Type: application/json" \
  --request POST \
  --data '{"recipient_email":"<EMAIL>","agent":"{type:Sender,status:packed,email:<EMAIL>}", 
  "coolerbox_id":"1","sensor_data":"{temperature:35,latitude:45.317,longitude:16.606,gps_timestamp:2023-07-24h16:34:23}",
  "order_id":"12345","exception_datetime":"2023-07-25T13:15:23"}'\
  http://localhost:8080/api/v1/logexception
  */
router.post('/api/v1/logexception', async (req, res)=>{
  const recipientEmail = req.body.recipient_email;
  const agent = req.body.agent;
  const coolerBoxID = req.body.coolerbox_id;
  const sensorData = req.body.sensor_data;
  const orderID = req.body.order_id;
  const exceptionDateTime = req.body.exception_datetime;

  const logID = uuidv4();
  const logDateTime = moment().format(dateTimeFormat);
  let apiMessage = "";
    
  if(recipientEmail && agent && coolerBoxID && sensorData && orderID && exceptionDateTime)
  {
    try{
      let orderData = `{order_id: ${orderID}, coolerbox_id: ${coolerBoxID}}`;
      console.log(`orderData ${orderData}`);

      var exceptionTransactionDetails = await scbContract.methods.addExceptionRecord(recipientEmail, 
        agent, orderData, sensorData, exceptionDateTime).send({ from: scbAccountAddress })
      
      let transactionHash = exceptionTransactionDetails.transactionHash
      let transactionURL = `${blockchainExplorerURL}${transactionHash}`
      apiMessage = `Exception with time ${exceptionDateTime} for order ${orderID} with recipient 
        ${recipientEmail} and sender details ${agent} logged to blockchain. Transaction hash is 
        ${transactionHash}.`

      console.log(apiMessage);
      console.log(exceptionTransactionDetails);
    
      //insert into DB
      const sql_insert = `INSERT INTO scb_exception (logid, recipient_email, agent, coolerbox_id, 
        sensor_data, order_id, exception_datetime, log_datetime, added_to_blockchain, 
        transaction_hash, blockchain_url) VALUES
        ('${logID}','${recipientEmail}', '${agent}', '${coolerBoxID}', '${sensorData}', '${orderID}',
         '${exceptionDateTime}', '${logDateTime}', 'True','${transactionHash}', '${transactionURL}');`;
        
        console.log(sql_insert);
        db.run(sql_insert, (err) => {
          if (err) {
            console.error(err.message);
            res.status(500).json({"status":"Failed", "message":"Database error occured."});
            return
          }
          console.log(`Successful creation of exception record for ${orderID} with exception time ${exceptionDateTime}, 
          recipient ${recipientEmail} and sender details ${agent}, with transaction hash ${transactionHash} and logID ${logID} in DB.`);
          res.json({"status":"Success", "message":apiMessage});
        });
    }
    catch(err){
      res.status(500).json({"status":"Failed", "message":"Error logging to blockchain."});
  }
}
else{
    apiMessage = "Please supply recipient_email, agent, coolerbox_id,\
    sensor_data, order_id & exceptionDateTime."
    res.status(400).json({"status":"Failed", "message":apiMessage});
  }
});

/*
get blockchain links for an order

GET /getBlockchainLinks
Requirements: orderID

curl --header "Content-Type: application/json" \
  --request GET \
  --data '{"order_id":"12345"}'\
  http://localhost:8080/api/v1/getblockchainlinks
  */
  router.get('/api/v1/getblockchainlinks', (req, res)=>{
    const orderID = req.body.order_id;
    let apiMessage = "";
  
    if(orderID){
      var sqlOrder = "SELECT ? as type, logid, order_id, order_datetime, blockchain_url from scb_order where order_id = ? \
      ORDER BY log_datetime ASC"
      var paramsOrder = ["order", orderID]
      db.all(sqlOrder, paramsOrder, (errOrder, rowsOrder) => {
          if (errOrder) {
            res.status(400).json({"error":errOrder.message});
            return
          }
          var sqlEvent = "SELECT ? as type, logid, order_id, event_datetime, blockchain_url from scb_event where order_id = ? \
          ORDER BY log_datetime ASC"
          var paramsEvent = ["event", orderID]
          db.all(sqlEvent, paramsEvent, (errEvent, rowsEvent) => {
              if (errEvent) {
                res.status(400).json({"error":errEvent.message});
                return
              }
              var sqlException = "SELECT ? as type, logid, order_id, exception_datetime, blockchain_url from scb_exception \
               where order_id = ? ORDER BY log_datetime ASC"
              var paramsException = ["exception", orderID]
              db.all(sqlException, paramsException, (errException, rowsException) => {
                  if (errException) {
                    res.status(400).json({"error":errException.message});
                    return
                  }
              apiMessage = `Order ${orderID} blockchain links.`
              combinedRows = [...rowsOrder, ...rowsEvent, ...rowsException]
              res.json({
                "status":"Success",
                "message":apiMessage,
                "data":combinedRows
              })
          });
        });
      });
    }else{
      apiMessage = "Please supply order_id."
      res.status(400).json({"status":"Failed", "message":apiMessage});
    }
  });


  /*
get blockchain link for an order

GET /orderBlockchainURL
Requirements: orderID

curl --header "Content-Type: application/json" \
  --request GET \
  --data '{"order_id":"12345"}'\
  http://localhost:8080/api/v1/orderblockchainurl
  */
  router.get('/api/v1/orderblockchainurl', (req, res)=>{
    const orderID = req.body.order_id;
    let apiMessage = "";
  
    if(orderID){
      var sql = "SELECT logid, order_id, order_datetime, blockchain_url from scb_order where order_id = ? \
      ORDER BY log_datetime ASC LIMIT 1"
      var params = [orderID]
      db.all(sql, params, (err, rows) => {
          if (err) {
            res.status(400).json({"error":err.message});
            return
          }
          apiMessage = `Order ${orderID} blockchain URL.`
          res.json({
            "status":"Success",
            "message":apiMessage,
            "data":rows
          })
        });
    }else{
      apiMessage = "Please supply order_id."
      res.status(400).json({"status":"Failed", "message":apiMessage});
    }
  });             
  
  /*
get all order, event and exception data for an order

GET /getorderdata
Requirements: orderID

curl --header "Content-Type: application/json" \
  --request GET \
  --data '{"order_id":"12345"}'\
  http://localhost:8080/api/v1/getorderdata
  */
  router.get('/api/v1/getorderdata', (req, res)=>{
    const orderID = req.body.order_id;
    let apiMessage = "";
  
    if(orderID){
      var sqlOrder = "SELECT ? as type, logid, sender_email, recipient_email, order_id, coolerbox_id, \
      order_datetime, log_datetime, added_to_blockchain, transaction_hash, blockchain_url \
      from scb_order where order_id = ? \
      ORDER BY log_datetime ASC"
      var paramsOrder = ["order", orderID]
      db.all(sqlOrder, paramsOrder, (errOrder, rowsOrder) => {
          if (errOrder) {
            res.status(400).json({"error":errOrder.message});
            return
          }
          var sqlEvent = "SELECT ? as type, logid, sender_email, recipient_email, courier_email, coolerbox_id, \
          sensor_data, agent_type, agent_status, order_id, event_datetime, log_datetime, added_to_blockchain, \
          transaction_hash, blockchain_url from scb_event where order_id = ? \
          ORDER BY log_datetime ASC"
          var paramsEvent = ["event", orderID]
          db.all(sqlEvent, paramsEvent, (errEvent, rowsEvent) => {
              if (errEvent) {
                res.status(400).json({"error":errEvent.message});
                return
              }
              var sqlException = "SELECT ? as type, logid, recipient_email, agent, coolerbox_id, sensor_data, order_id, \
              exception_datetime, log_datetime,  added_to_blockchain, transaction_hash, blockchain_url from scb_exception \
               where order_id = ? ORDER BY log_datetime ASC"
              var paramsException = ["exception", orderID]
              db.all(sqlException, paramsException, (errException, rowsException) => {
                  if (errException) {
                    res.status(400).json({"error":errException.message});
                    return
                  }
              apiMessage = `Order ${orderID} blockchain links.`
              combinedRows = [...rowsOrder, ...rowsEvent, ...rowsException]
              res.json({
                "status":"Success",
                "message":apiMessage,
                "data_order":rowsOrder,
                "data_event":rowsEvent,
                "data_exception":rowsException,
              })
          });
        });
      });
    }else{
      apiMessage = "Please supply order_id."
      res.status(400).json({"status":"Failed", "message":apiMessage});
    }
  });

/* 
API routes to create

GET /orderdata
Requirements: orderID

GET /eventBlockchainURL
Requirements: orderID, eventID

GET /exceptionBlockchainURL
Requirements: orderID, exceptionID

*/

/*
Data models (SQLite and track blockchain URL?):
- sender
- receiver
- courier
- parcel
- supplyChainEvent
- supplyChainException
- sensor readings sensor, reading, datetime, parcel, custodian (sender, courier, receiver), custodianID
- sensor exception readings - sensor, reading, datetime, parcel, custodian (sender, courier, receiver), custodianID
*/

module.exports = router;