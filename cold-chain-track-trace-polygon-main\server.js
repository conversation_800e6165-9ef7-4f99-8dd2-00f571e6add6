// import libraries
const express = require('express');
const createError = require('http-errors');
const path = require('path');
const logger = require('morgan');
const fs = require('fs');

// load environment variables from a .env file into process.env
require('dotenv').config() 

// import routers
const indexRouter = require('./routes/index');
const apiV1Router = require('./routes/api_v1');
const apiTestRouter = require('./routes/api_test')

// import database object
var db = require('./data/db')

// create express application
const app = express();

// create a write stream (in append mode), use {flags: 'w'} to open in write mode
const logFile = fs.createWriteStream('./scb_logfile.log', {flags: 'a'}); 

/* setup logging
Middleware that you want to get called for all routes must be defined BEFORE any of the route definitions that you want it to 
run before. The order of registering these matters.
https://expressjs.com/en/resources/middleware/morgan.html
*/
 // log to file
// app.use(logger('short', { stream: logFile }));

// custom morgan logging format based on short pattern but prefixed by UTC date
const morganShortWithDate = ' [:date[clf]] :remote-addr :remote-user:method :url HTTP/:http-version :status :res[content-length] - :response-time ms'
app.use(logger(morganShortWithDate, { stream: logFile }));


// log to console
app.use(logger(morganShortWithDate)); 

/*
To log console.log to a file
node server.js > scb_logfile_dev.log 2>&1

This would redirect stdout to a file named scb_logfile_dev.log and redirect stderr to stdout.

*/

// setup serving of static files
app.use(express.static(path.join(__dirname, 'public')));

// view engine setup (ejs)
app.set('views', path.join(__dirname, 'views'));
app.set('view engine', 'ejs');

// middleware that enables use of JSON in the API i.e. parse 'application/json' content-type
app.use(express.json()); 

// middleware for parsing incoming requests with URL-encoded payloads i.e. parse content-type: application/x-www-form-urlencoded e.g. POST's from a form
app.use(express.urlencoded({ extended: true }));

app.enable('verbose errors');

// disable them in production
// use $ NODE_ENV=production node examples/error-pages
if (app.settings.env === 'production') app.disable('verbose errors')

// mount routers
app.use('/', indexRouter);
app.use('/', apiV1Router);
app.use('/', apiTestRouter);

// catch favicon.ico request and send 204 No Content status (or else add favicon.ico to root)
app.get('/favicon.ico', (req, res) => res.status(204).end());

// catch 404 and forward to error handler
app.use(function(req, res, next) {
    next(createError(404));
  });
  
// error-handling middleware, defined last, after other app.use() and routes calls
app.use(function(err, req, res, next) {
    console.error(err.stack)

    // set locals, only providing error in development
    res.locals.message = err.message;
    res.locals.error = req.app.get('env') === 'development' ? err : {};

    // render the error page
    res.status(err.status || 500);
    res.render('pages/error');
});

const port = process.env.PORT || 8080;
app.listen(port, () => {
  console.log(`Server is listening on port ${port}...`);
  console.log(`Successfully started SmartCoolerBox Rest API...`);
});