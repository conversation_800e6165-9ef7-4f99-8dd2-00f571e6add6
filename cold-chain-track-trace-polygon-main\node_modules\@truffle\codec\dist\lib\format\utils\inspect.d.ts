/// <reference types="node" />
import util from "util";
import * as Format from "../common";
import * as Common from "../../common";
export interface InspectOptions {
    stylize?: (toMaybeColor: string, style?: string) => string;
    colors: boolean;
    breakLength: number;
}
export interface ResultInspectorOptions {
    /**
     * This option causes the [[ResultInspector]] to display, for
     * addresses with a reverse ENS record, both the ENS name and
     * the address.  (By default it displays only the ENS name.)
     */
    noHideAddress?: boolean;
    /**
     * This flag, if set, causes mappings to be rendered via objects
     * rather than Maps.  This is intended for compatibility and not
     * recommended for normal use.
     */
    renderMappingsViaObjects?: boolean;
}
/**
 * This class is meant to be used with Node's
 * [util.inspect()](https://nodejs.org/api/util.html#util_util_inspect_object_options)
 * function.  Given a [[Format.Values.Result]] `value`, one can use
 * `new ResultInspector(value)` to create a ResultInspector for that value,
 * which can be used with util.inspect() to create a human-readable string
 * representing the value.
 *
 * @example
 * Suppose `value` is a Result.  In Node, the following would print to the
 * console a human-readable representation of `value`, with colors enabled,
 * no maximum depth, and no maximum array length, and lines (usually) no
 * longer than 80 characters:
 * ```javascript
 * console.log(
 *   util.inspect(
 *     new ResultInspector(value),
 *     {
 *       colors: true,
 *       depth: null,
 *       maxArrayLength: null,
 *       breakLength: 80
 *     }
 *   )
 * );
 * ```
 * Of course, there are many other ways to use util.inspect; see Node's
 * documentation, linked above, for more.
 */
export declare class ResultInspector {
    result: Format.Values.Result;
    options: ResultInspectorOptions;
    constructor(result: Format.Values.Result, options?: ResultInspectorOptions);
    /**
     * @dev non-standard alternative interface name used by browser-util-inspect
     *      package
     */
    inspect(depth: number | null, options: InspectOptions): string;
    [util.inspect.custom](depth: number | null, options: InspectOptions): string;
}
/**
 * WARNING! Do NOT use this function in real code unless you
 * absolutely have to!  Using it in controlled tests is fine,
 * but do NOT use it in real code if you have any better option!
 * See [[unsafeNativize]] for why!
 */
export declare function unsafeNativizeVariables(variables: {
    [name: string]: Format.Values.Result;
}): {
    [name: string]: any;
};
/**
 * WARNING! Do NOT use this function in real code unless you absolutely have
 * to!  Using it in controlled tests is fine, but do NOT use it in real code if
 * you have any better option!
 *
 * This function is a giant hack.  It will throw exceptions on numbers that
 * don't fit in a Javascript number.  It loses various information.  It was
 * only ever written to support our hacked-together watch expression system,
 * and later repurposed to make testing easier.
 *
 * If you are not doing something as horrible as evaluating user-inputted
 * Javascript expressions meant to operate upon Solidity variables, then you
 * probably have a better option than using this in real code!
 *
 * (For instance, if you just want to nicely print individual values, without
 * attempting to first operate on them via Javascript expressions, we have the
 * [[ResultInspector]] class, which can be used with Node's
 * [util.inspect()](https://nodejs.org/api/util.html#util_util_inspect_object_options)
 * to do exactly that.)
 *
 * Remember, the decoder output format was made to be machine-readable.  It
 * shouldn't be too hard for you to process.  If it comes to it, copy-paste
 * this code and dehackify it for your use case, which hopefully is more
 * manageable than the one that caused us to write this.
 */
export declare function unsafeNativize(result: Format.Values.Result): any;
/**
 * Turns a wrapped access list into a usable form.
 * Will fail if the input is not a wrapped access list!
 * Note that the storage keys must be given as uint256, not bytes32.
 * Primarily meant for internal use.
 */
export declare function nativizeAccessList(wrappedAccessList: Format.Values.ArrayValue): Common.AccessList;
export declare function stringValueInfoToStringLossy(info: Format.Values.StringValueInfo): string;
