const sqlite3 = require('sqlite3').verbose()
const path = require('path');

const DBSOURCE = "scb_db.sqlite"

// Connection to the SQlite database
const db_name = path.join(__dirname, DBSOURCE);
console.log("Database full path - " + db_name);
const db = new sqlite3.Database(db_name, (err) => {
  if (err) {
    return console.error(err.message);
  }
  console.log(`Successful connection to the database ${DBSOURCE}`);
});

/*
Create order table 
Columns: logid, sender_email, recipient_email, order_id, coolerbox_id, order_datetime, log_datetime, added_to_blockchain,
transaction_hash, blockchain_url)
*/
const sqlCreateOrderTable = `CREATE TABLE IF NOT EXISTS scb_order (
    logid TEXT PRIMARY KEY,
    sender_email TEXT NOT NULL,
    recipient_email TEXT NOT NULL,
    order_id TEXT NOT NULL,
    coolerbox_id TEXT NOT NULL,
    order_datetime TEXT NOT NULL,
    log_datetime TEXT NOT NULL,
    added_to_blockchain TEXT,
    transaction_hash TEXT,
    blockchain_url TEXT
  );`;

db.run(sqlCreateOrderTable,(err) => {
    if (err) {
        return console.error(err.message);
    }
    console.log("Successful initialisation of the 'scb_order' table");
});  

/*
Create event table 
Columns: logid, sender_email, recipient_email, courier_email, coolerbox_id, sensor_data, agent_type, agent_status, order_id, 
event_datetime, log_datetime, added_to_blockchain, transaction_hash, blockchain_url)
*/
const sqlCreateEventTable = `CREATE TABLE IF NOT EXISTS scb_event (
    logid TEXT PRIMARY KEY,
    sender_email TEXT NOT NULL,
    recipient_email TEXT NOT NULL,
    courier_email TEXT NOT NULL,
    coolerbox_id TEXT NOT NULL,
    sensor_data TEXT NOT NULL,
    agent_type TEXT NOT NULL,
    agent_status TEXT NOT NULL,
    order_id TEXT NOT NULL,
    event_datetime TEXT NOT NULL,
    log_datetime TEXT NOT NULL,
    added_to_blockchain TEXT,
    transaction_hash TEXT,
    blockchain_url TEXT
  );`;

db.run(sqlCreateEventTable,(err) => {
    if (err) {
        return console.error(err.message);
    }
    console.log("Successful initialisation of the 'scb_event' table.");
});  

/*
Create exception table 
Columns: logid, recipient_email, agent, coolerbox_id, sensor_data, order_id, exception_datetime, log_datetime, 
added_to_blockchain, transaction_hash, blockchain_url)
*/
const sqlCreateExceptionTable = `CREATE TABLE IF NOT EXISTS scb_exception (
    logid TEXT PRIMARY KEY,
    recipient_email TEXT NOT NULL,
    agent TEXT NOT NULL,
    coolerbox_id TEXT NOT NULL,
    sensor_data TEXT NOT NULL,
    order_id TEXT NOT NULL,
    exception_datetime TEXT,
    log_datetime TEXT,
    added_to_blockchain TEXT,
    transaction_hash TEXT,
    blockchain_url TEXT
  );`;

db.run(sqlCreateExceptionTable,(err) => {
    if (err) {
        return console.error(err.message);
    }
    console.log("Successful initialisation of the 'scb_exception' table");
});  

module.exports = db