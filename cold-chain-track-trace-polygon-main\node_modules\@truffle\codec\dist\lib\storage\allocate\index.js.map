{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../lib/storage/allocate/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAAgC;AAChC,MAAM,KAAK,GAAG,IAAA,eAAW,EAAC,wBAAwB,CAAC,CAAC;AAEpD,yDAAoD;AACpD,qDAAgD;AAChD,mDAA8C;AAE9C,gDAAsD;AACtD,+CAA0C;AAY1C,+CAA0C;AAC1C,qDAAgD;AAChD,kDAAuB;AACvB,iEAAyC;AAWzC,MAAa,0BAA2B,SAAQ,KAAK;IAKnD,YACE,SAAiB,EACjB,WAAmB,EACnB,WAAmB,EACnB,MAAc;QAEd,MAAM,OAAO,GAAG,kCAAkC,MAAM,OAAO,WAAW,IAAI,WAAW,QAAQ,SAAS,GAAG,CAAC;QAC9G,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,4BAA4B,CAAC;QACzC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;CACF;AAnBD,gEAmBC;AAOD,6EAA6E;AAC7E,yDAAyD;AACzD,SAAgB,qBAAqB,CACnC,6BAAmE;IAEnE,IAAI,WAAW,GAAuB,EAAE,CAAC;IACzC,KAAK,MAAM,WAAW,IAAI,MAAM,CAAC,MAAM,CAAC,6BAA6B,CAAC,EAAE;QACtE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,gBAAgB,EAAE,GAAG,WAAW,CAAC;QAC1D,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;YACvD,IAAI,QAAQ,CAAC,SAAS,KAAK,QAAQ,EAAE;gBACnC,IAAI;oBACF,WAAW,GAAG,cAAc,CAC1B,QAAQ,EACR,gBAAgB,EAChB,WAAW,EACX,QAAQ,CACT,CAAC;iBACH;gBAAC,WAAM;oBACN,qFAAqF;oBACrF,oEAAoE;oBACpE,wEAAwE;oBACxE,wEAAwE;oBACxE,4BAA4B;iBAC7B;aACF;SACF;KACF;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AA1BD,sDA0BC;AAED;;;;;;;;GAQG;AACH,SAAgB,mBAAmB,CACjC,SAAmC,EACnC,qBAAgE,EAChE,gBAAwC,EACxC,kBAAsC,EACtC,sBAAwC,EAAE;IAE1C,IAAI,WAAW,GAAG,mBAAmB,CAAC;IACtC,KAAK,MAAM,YAAY,IAAI,SAAS,EAAE;QACpC,IAAI,EACF,YAAY,EAAE,QAAQ,EACtB,mBAAmB,EACnB,QAAQ,EACR,aAAa,EACd,GAAG,YAAY,CAAC;QACjB,IAAI;YACF,WAAW,GAAG,qBAAqB,CACjC,QAAQ,EACR,mBAAmB,EACnB,aAAa,EACb,QAAQ,EACR,qBAAqB,CAAC,aAAa,CAAC,EACpC,gBAAgB,EAChB,kBAAkB,EAClB,WAAW,CACZ,CAAC;SACH;QAAC,WAAM;YACN,wEAAwE;SACzE;KACF;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AA/BD,kDA+BC;AAED,SAAS,cAAc,CACrB,QAAiC,EACjC,gBAAwC,EACxC,mBAAuC,EACvC,QAAmC;IAEnC,gDAAgD;IAChD,6CAA6C;IAC7C,OAAO,eAAe,CACpB,QAAQ,CAAC,EAAE,EACX,QAAQ,CAAC,WAAW,EACpB,gBAAgB,EAChB,mBAAmB,EACnB,QAAQ,CACT,CAAC;AACJ,CAAC;AAED,SAAS,eAAe,CACtB,QAAgB,EAChB,OAAoC,EACpC,gBAAwC,EACxC,mBAAuC,EACvC,QAAmC;IAEnC,IAAI,MAAM,GAAW,CAAC,CAAC,CAAC,yCAAyC;IACjE,IAAI,KAAK,GAAW,GAAG,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC;IAE5C,wDAAwD;IACxD,IAAI,QAAQ,IAAI,mBAAmB,EAAE;QACnC,OAAO,mBAAmB,CAAC;KAC5B;IAED,IAAI,WAAW,qBAAQ,mBAAmB,CAAE,CAAC,CAAC,wDAAwD;IAEtG,gCAAgC;IAChC,IAAI,iBAAiB,GAA8B,EAAE,CAAC;IAEtD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;QAC5B,IAAI,IAA2B,CAAC;QAChC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,sBAAsB,CAC7C,MAAM,CAAC,IAAI,EACX,gBAAgB,EAChB,WAAW,EACX,QAAQ,CACT,CAAC,CAAC;QAEH,4FAA4F;QAC5F,6EAA6E;QAC7E,IACE,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC;YACvB,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC;YACjC,CAAC,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,EAC1B;YACA,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC;YAChC,MAAM,IAAI,CAAC,CAAC;SACb;QACD,+BAA+B;QAE/B,IAAI,KAAoB,CAAC;QAEzB,IAAI,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;YAC7B,YAAY;YACZ,KAAK,GAAG;gBACN,IAAI,EAAE;oBACJ,IAAI,EAAE;wBACJ,MAAM,EAAE,IAAI,eAAE,CAAC,MAAM,CAAC,CAAC,8BAA8B;qBACtD;oBACD,KAAK,EAAE,CAAC,CAAC,kCAAkC;iBAC5C;gBACD,EAAE,EAAE;oBACF,IAAI,EAAE;wBACJ,MAAM,EAAE,IAAI,eAAE,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,oDAAoD;qBAC7F;oBACD,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,4BAA4B;iBAC5D;aACF,CAAC;SACH;aAAM;YACL,YAAY;YACZ,KAAK,GAAG;gBACN,IAAI,EAAE;oBACJ,IAAI,EAAE;wBACJ,MAAM,EAAE,IAAI,eAAE,CAAC,MAAM,CAAC,CAAC,8BAA8B;qBACtD;oBACD,KAAK,EAAE,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,gDAAgD;iBACjF;gBACD,EAAE,EAAE;oBACF,IAAI,EAAE;wBACJ,MAAM,EAAE,IAAI,eAAE,CAAC,MAAM,CAAC,CAAC,4BAA4B;qBACpD;oBACD,KAAK,EAAE,KAAK,CAAC,6BAA6B;iBAC3C;aACF,CAAC;SACH;QACD,iBAAiB,CAAC,IAAI,CAAC;YACrB,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,OAAO,EAAE;gBACP,QAAQ,EAAE,SAAS;gBACnB,KAAK;aACN;SACF,CAAC,CAAC;QACH,uCAAuC;QACvC,kFAAkF;QAClF,IAAI,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;YAC7B,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC;YACrB,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC;SACjC;QACD,qEAAqE;aAChE;YACH,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC;YACpB,gEAAgE;YAChE,IAAI,KAAK,GAAG,CAAC,EAAE;gBACb,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC;gBAChC,MAAM,IAAI,CAAC,CAAC;aACb;SACF;KACF;IAED,2EAA2E;IAC3E,+BAA+B;IAC/B,gFAAgF;IAChF,uEAAuE;IACvE,8EAA8E;IAC9E,uDAAuD;IACvD,IAAI,SAAgC,CAAC;IACrC,IAAI,KAAK,KAAK,GAAG,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,IAAI,MAAM,KAAK,CAAC,EAAE;QACrD,SAAS,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;KAC/B;SAAM;QACL,SAAS,GAAG,EAAE,KAAK,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC;KACnC;IAED,0DAA0D;IAC1D,WAAW,CAAC,QAAQ,CAAC,GAAG;QACtB,OAAO,EAAE,iBAAiB;QAC1B,IAAI,EAAE,SAAS;KAChB,CAAC;IAEF,oBAAoB;IACpB,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,iBAAiB,CAAC,YAAyB;IAClD,8BAA8B;IAC9B,OAAO,YAAY,CAAC,KAAK,CAAC,MAAM,CAC9B,CAAC,IAAiB,EAAE,EAAE,CACpB,IAAI,CAAC,QAAQ,KAAK,qBAAqB,IAAI,IAAI,CAAC,aAAa,CAChE,CAAC;AACJ,CAAC;AAED,SAAS,qBAAqB,CAC5B,QAAqB,EACrB,mBAAwC,EACxC,aAAqB,EACrB,QAAkC,EAClC,qBAAmC,EACnC,gBAAwC,EACxC,kBAAsC,EACtC,sBAAwC,EAAE;IAE1C,uCAAuC;IACvC,IAAI,WAAW,GAAqB,MAAM,CAAC,MAAM,CAC/C,EAAE,EACF,GAAG,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,GAAG,CACxC,CAAC,CAAC,aAAa,EAAE,sBAAsB,CAAC,EAAE,EAAE,CAAC,CAAC;QAC5C,CAAC,aAAa,CAAC,oBAAO,sBAAsB,CAAE;KAC/C,CAAC,CACH,CACF,CAAC;IACF,IAAI,CAAC,mBAAmB,EAAE;QACxB,mBAAmB,GAAG,EAAE,CAAC,CAAC,yCAAyC;KACpE;IAED,iEAAiE;IACjE,qEAAqE;IACrE,wBAAwB;IACxB,IAAI,+BAA+B,GACjC,QAAQ,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,CAAC;IAErD,wDAAwD;IACxD,IAAI,SAAS,GAAG,EAAE,CAAC,MAAM,CACvB,GAAG,+BAA+B,CAAC,GAAG,CAAC,CAAC,EAAU,EAAE,EAAE;QACpD,IAAI,QAAQ,GAAG,qBAAqB,CAAC,EAAE,CAAC,CAAC;QACzC,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,MAAM,IAAI,0BAA0B,CAClC,QAAQ,CAAC,EAAE,EACX,QAAQ,CAAC,IAAI,EACb,QAAQ,CAAC,YAAY,EACrB,EAAE,CACH,CAAC;SACH;QACD,OAAO,iBAAiB,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YACpD,UAAU;YACV,SAAS,EAAE,QAAQ;SACpB,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CACH,CAAC;IAEF,mDAAmD;IACnD,MAAM,UAAU,GAAG,CAAC,UAAuB,EAAE,EAAE,CAC7C,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,UAAU,KAAK,UAAU,CAAC;IAE9D,8DAA8D;IAC9D,IAAI,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,GAAG,IAAA,mBAAS,EAAC,SAAS,EAAE,QAAQ,CAAC,EAAE,CAC3E,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,CAChC,CAAC;IAEF,gDAAgD;IAChD,wBAAwB;IACxB,qEAAqE;IACrE,uEAAuE;IACvE,MAAM,WAAW,GAAG,CAAC,UAAuB,EAAE,EAAE,CAC9C,UAAU,CAAC,UAAU,KAAK,WAAW;QACrC,UAAU,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,mBAAmB,CAAC;IAElD,IAAI,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,GAAG,IAAA,mBAAS,EACpD,iBAAiB,EACjB,QAAQ,CAAC,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,CAC7C,CAAC;IAEF,6CAA6C;IAC7C,MAAM,oBAAoB,GAAG,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAC7D,IAAI,EAAE,QAAQ,CAAC,UAAU,CAAC,IAAI;QAC9B,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,gBAAgB,CAC/B,QAAQ,CAAC,UAAU,EACnB,aAAa,EACb,QAAQ,CACT;KACF,CAAC,CAAC,CAAC;IAEJ,4DAA4D;IAC5D,MAAM,EAAE,GAAG,IAAI,CAAC;IAChB,MAAM,iCAAiC,GAAG,eAAe,CACvD,EAAE,EACF,oBAAoB,EACpB,gBAAgB,EAChB,kBAAkB,EAClB,QAAQ,CACT,CAAC,EAAE,CAAC,CAAC;IAEN,yBAAyB;IACzB,MAAM,0BAA0B,GAAG,gBAAgB,CAAC,GAAG,CACrD,CAAC,EAAE,UAAU,EAAE,SAAS,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;QACrC,UAAU;QACV,SAAS;QACT,aAAa;QACb,OAAO,EAAE,iCAAiC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,OAAO;KAClE,CAAC,CACH,CAAC;IAEF,iDAAiD;IACjD,IAAI,4BAA4B,GAAG,kBAAkB,CAAC,GAAG,CACvD,CAAC,EAAE,UAAU,EAAE,SAAS,EAAE,EAAE,EAAE;QAC5B,IAAI,UAAU,GAAG,mBAAmB,CAAC,UAAU,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;QACrE,IAAI,OAAgC,CAAC;QACrC,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YAC3B,OAAO,GAAG;gBACR,QAAQ,EAAE,SAAkB;aAC7B,CAAC;SACH;aAAM;YACL,OAAO,GAAG;gBACR,QAAQ,EAAE,MAAe;gBACzB,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK;gBAC1B,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM;aAC7B,CAAC;SACH;QACD,OAAO;YACL,UAAU;YACV,SAAS;YACT,aAAa;YACb,OAAO;SACR,CAAC;IACJ,CAAC,CACF,CAAC;IAEF,gDAAgD;IAChD,IAAI,2BAA2B,GAAG,iBAAiB,CAAC,GAAG,CACrD,CAAC,EAAE,UAAU,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;QAC9B,UAAU;QACV,SAAS;QACT,aAAa;QACb,OAAO,EAAE;YACP,QAAQ,EAAE,YAAqB;YAC/B,UAAU,EAAE,UAAU,CAAC,KAAK;SAC7B;KACF,CAAC,CACH,CAAC;IAEF,iCAAiC;IACjC,IAAI,kBAAkB,GAA8B,EAAE,CAAC;IACvD,KAAK,IAAI,QAAQ,IAAI,SAAS,EAAE;QAC9B,IAAI,eAAe,GAAG,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC;YACnD,CAAC,CAAC,2BAA2B;YAC7B,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC;gBAClC,CAAC,CAAC,4BAA4B;gBAC9B,CAAC,CAAC,0BAA0B,CAAC;QAC/B,kBAAkB,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,uCAAuC;KAC1F;IAED,gCAAgC;IAChC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,EAAE;QAC/B,WAAW,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC;KACjC;IACD,WAAW,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG;QACxC,OAAO,EAAE,kBAAkB;KAC5B,CAAC;IAEF,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,oFAAoF;AACpF,sFAAsF;AACtF,mEAAmE;AACnE,SAAgB,WAAW,CACzB,QAA2B,EAC3B,gBAAyC,EACzC,WAAgC,EAChC,QAAmC;IAEnC,OAAO,sBAAsB,CAC3B,QAAQ,EACR,gBAAgB,EAChB,WAAW,EACX,QAAQ,CACT,CAAC,IAAI,CAAC;AACT,CAAC;AAZD,kCAYC;AAED,SAAS,sBAAsB,CAC7B,QAA2B,EAC3B,gBAAyC,EACzC,mBAAwC,EACxC,QAAmC;IAEnC,kDAAkD;IAClD,0EAA0E;IAC1E,qBAAqB;IACrB,QAAQ,QAAQ,CAAC,SAAS,EAAE;QAC1B,KAAK,OAAO,CAAC,CAAC;YACZ,QAAQ,QAAQ,CAAC,IAAI,EAAE;gBACrB,KAAK,QAAQ;oBACX,wBAAwB;oBACxB,OAAO;wBACL,IAAI,EAAE;4BACJ,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,gBAAgB,CAAC;yBAC7D;wBACD,WAAW,EAAE,mBAAmB;qBACjC,CAAC;gBACJ,KAAK,SAAS;oBACZ,OAAO;wBACL,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE;wBAClB,WAAW,EAAE,mBAAmB;qBACjC,CAAC;aACL;SACF;QAED,KAAK,QAAQ,CAAC;QACd,KAAK,SAAS;YACZ,OAAO;gBACL,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE;gBAClB,WAAW,EAAE,mBAAmB;aACjC,CAAC;QAEJ,KAAK,OAAO,CAAC,CAAC;YACZ,QAAQ,QAAQ,CAAC,IAAI,EAAE;gBACrB,KAAK,SAAS;oBACZ,OAAO;wBACL,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE;wBAClB,WAAW,EAAE,mBAAmB;qBACjC,CAAC;gBACJ,KAAK,QAAQ;oBACX,mBAAmB;oBACnB,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,gDAAgD;oBAC3F,IAAI,MAAM,KAAK,CAAC,EAAE;wBAChB,mFAAmF;wBACnF,OAAO;4BACL,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE;4BAClB,WAAW,EAAE,mBAAmB;yBACjC,CAAC;qBACH;oBACD,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,sBAAsB,CAC1D,QAAQ,CAAC,QAAQ,EACjB,gBAAgB,EAChB,mBAAmB,CACpB,CAAC;oBACF,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE;wBAClC,YAAY;wBACZ,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;wBACjE,KAAK,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;wBAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC;wBAC7C,OAAO;4BACL,IAAI,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE;4BACzB,WAAW;yBACZ,CAAC;qBACH;yBAAM;wBACL,YAAY;wBACZ,OAAO;4BACL,IAAI,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,GAAG,MAAM,EAAE;4BACxC,WAAW;yBACZ,CAAC;qBACH;aACJ;SACF;QAED,KAAK,QAAQ,CAAC,CAAC;YACb,IAAI,WAAW,GAAuB,mBAAmB,CAAC;YAC1D,IAAI,UAAU,GAAkC,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,mBAAmB;YAC7F,IAAI,UAAU,KAAK,SAAS,EAAE;gBAC5B,2EAA2E;gBAC3E,MAAM,UAAU,GAA4B,CAC1C,gBAAgB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAC9B,CAAC;gBACF,IAAI,CAAC,UAAU,EAAE;oBACf,MAAM,IAAI,MAAM,CAAC,2BAA2B,CAC1C,QAAQ,CAAC,EAAE,EACX,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAClC,CAAC;iBACH;gBACD,WAAW,GAAG,cAAc,CAC1B,UAAU,EACV,gBAAgB,EAChB,mBAAmB,CACpB,CAAC;gBACF,UAAU,GAAG,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;aACvC;YACD,2DAA2D;YAC3D,OAAO;gBACL,IAAI,EAAE,UAAU,CAAC,IAAI;gBACrB,WAAW;aACZ,CAAC;SACH;QAED,KAAK,sBAAsB;YACzB,IAAI,QAAQ,CAAC,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;gBACxD,qEAAqE;gBACrE,oEAAoE;gBACpE,qEAAqE;gBACrE,OAAO;oBACL,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE;oBAClB,WAAW,EAAE,mBAAmB;iBACjC,CAAC;aACH;QACH,gCAAgC;QAChC,yBAAyB;QACzB;YACE,+BAA+B;YAC/B,OAAO;gBACL,IAAI,EAAE;oBACJ,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,gBAAgB,CAAC;iBAC7D;gBACD,WAAW,EAAE,mBAAmB;aACjC,CAAC;KACL;AACH,CAAC"}