{"version": 3, "file": "abify.js", "sourceRoot": "", "sources": ["../../lib/abify.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAAgC;AAChC,MAAM,KAAK,GAAG,IAAA,eAAW,EAAC,aAAa,CAAC,CAAC;AAEzC,iDAAgD;AAChD,iDAAgD;AAMhD,yDAAwD;AAExD,4BAA4B;AAC5B,SAAgB,SAAS,CACvB,QAA2B,EAC3B,gBAAyC;IAEzC,QAAQ,QAAQ,CAAC,SAAS,EAAE;QAC1B,yDAAyD;QACzD,wDAAwD;QACxD,YAAY;QACZ,0DAA0D;QAC1D,wCAAwC;QACxC,KAAK,SAAS,CAAC;QACf,KAAK,OAAO,CAAC;QACb,KAAK,MAAM,CAAC;QACZ,KAAK,SAAS;YACZ,OAAO,SAAS,CAAC;QACnB,0DAA0D;QAC1D,KAAK,SAAS,CAAC;QACf,KAAK,UAAU;YACb,OAAO;gBACL,SAAS,EAAE,SAAS;gBACpB,IAAI,EAAE,SAAS;gBACf,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC;aAC5C,CAAC;QACJ,KAAK,UAAU;YACb,QAAQ,QAAQ,CAAC,UAAU,EAAE;gBAC3B,KAAK,UAAU;oBACb,OAAO;wBACL,SAAS,EAAE,UAAU;wBACrB,UAAU,EAAE,UAAU;wBACtB,IAAI,EAAE,SAAS;wBACf,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC;qBAC5C,CAAC;gBACJ,KAAK,UAAU,EAAE,2BAA2B;oBAC1C,OAAO,SAAS,CAAC;aACpB;YACD,MAAM,CAAC,uBAAuB;QAChC,kCAAkC;QAClC,KAAK,QAAQ,CAAC,CAAC;YACb,MAAM,QAAQ,GAA4B,CACxC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAClD,CAAC;YACF,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;gBACzB,IAAI,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBACtD,MAAM,IAAI,MAAM,CAAC,2BAA2B,CAC1C,QAAQ,CAAC,EAAE,EACX,aAAa,CACd,CAAC;aACH;YACD,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC,GAAG,CAC1C,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,CAAC;gBAC/B,IAAI;gBACJ,IAAI,EAAE,SAAS,CAAC,UAAU,EAAE,gBAAgB,CAAC;aAC9C,CAAC,CACH,CAAC;YACF,OAAO;gBACL,SAAS,EAAE,OAAO;gBAClB,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC;gBAC3C,WAAW;aACZ,CAAC;SACH;QACD,KAAK,MAAM,CAAC,CAAC;YACX,MAAM,QAAQ,GAA0B,CACtC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAClD,CAAC;YACF,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;gBACrB,IAAI,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBACtD,MAAM,IAAI,MAAM,CAAC,2BAA2B,CAC1C,QAAQ,CAAC,EAAE,EACX,aAAa,CACd,CAAC;aACH;YACD,IAAI,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC;YACzC,IAAI,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;YACpD,OAAO;gBACL,SAAS,EAAE,MAAM;gBACjB,IAAI;gBACJ,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC;aAC5C,CAAC;SACH;QACD,KAAK,sBAAsB,CAAC,CAAC;YAC3B,MAAM,QAAQ,GAA0C,CACtD,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAClD,CAAC;YACF,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;gBAC5B,IAAI,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBACtD,MAAM,IAAI,MAAM,CAAC,2BAA2B,CAC1C,QAAQ,CAAC,EAAE,EACX,aAAa,CACd,CAAC;aACH;YACD,MAAM,iBAAiB,GAAG,SAAS,CACjC,QAAQ,CAAC,cAAc,EACvB,gBAAgB,CACjB,CAAC;YACF,uCACK,iBAAiB,KACpB,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,yBAAyB,CAAC,QAAQ,CAAC,IAC1D;SACH;QACD,iBAAiB;QACjB,KAAK,OAAO;YACV,uCACK,QAAQ,KACX,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,EAC3C,QAAQ,EAAE,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,gBAAgB,CAAC,IACxD;QACJ,gCAAgC;QAChC;YACE,OAAO,QAAQ,CAAC;KACnB;AACH,CAAC;AA9GD,8BA8GC;AAED,4BAA4B;AAC5B,SAAgB,WAAW,CACzB,MAA4B,EAC5B,gBAAyC;IAEzC,QAAQ,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE;QAC7B,KAAK,SAAS,CAAC,CAAC,mBAAmB;QACnC,KAAK,OAAO,CAAC,CAAC,mBAAmB;QACjC,KAAK,MAAM,EAAE,mBAAmB;YAC9B,OAAO,SAAS,CAAC;QACnB,KAAK,SAAS;YACZ,0CAA0C;YAC1C,uCACmC,MAAO,KACxC,IAAI,EAA4B,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,gBAAgB,CAAC,IACxE;QACJ,KAAK,UAAU,CAAC,CAAC;YACf,IAAI,aAAa,GAAiC,MAAM,CAAC;YACzD,QAAQ,aAAa,CAAC,IAAI,EAAE;gBAC1B,KAAK,OAAO;oBACV,OAAO;wBACL,IAAI,EAA4B,CAC9B,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,gBAAgB,CAAC,CACzC;wBACD,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE;4BACL,SAAS,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO;4BACtC,QAAQ,EAAE,aAAa,CAAC,KAAK,CAAC,UAAU;yBACzC;wBACD,eAAe,EAAE,aAAa,CAAC,eAAe;qBAC/C,CAAC;gBACJ,KAAK,OAAO;oBACV,QAAQ,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE;wBAChC,KAAK,sBAAsB;4BACzB,OAAO;gCACL,IAAI,EAA4B,CAC9B,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,gBAAgB,CAAC,CACzC;gCACD,IAAI,EAAE,OAAO;gCACb,KAAK,EAAE;oCACL,IAAI,EAAE,qBAAqB;oCAC3B,WAAW,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW;oCAC5C,GAAG,EAAE,aAAa,CAAC,KAAK,CAAC,GAAG;iCAC7B;6BACF,CAAC;wBACJ;4BACE,2CAA2C;4BAC3C,uCAAuC;4BACvC,OAAO,gCACF,aAAa,KAChB,IAAI,EAA4B,CAC9B,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,gBAAgB,CAAC,CACzC,GACF,CAAC;qBACL;aACJ;YACD,MAAM,CAAC,uBAAuB;SAC/B;QACD,KAAK,UAAU;YACb,QAAQ,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE;gBAC9B,KAAK,UAAU,CAAC,CAAC;oBACf,IAAI,aAAa,GAAyC,MAAM,CAAC;oBACjE,uCACK,aAAa,KAChB,IAAI,EAAqC,CACvC,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,gBAAgB,CAAC,CACzC,IACD;iBACH;gBACD,KAAK,UAAU,EAAE,2BAA2B;oBAC1C,OAAO,SAAS,CAAC;aACpB;YACD,MAAM,CAAC,uBAAuB;QAChC,KAAK,QAAQ,CAAC,CAAC;YACb,IAAI,aAAa,GAA+B,MAAM,CAAC;YACvD,QAAQ,aAAa,CAAC,IAAI,EAAE;gBAC1B,KAAK,OAAO;oBACV,IAAI,aAAa,CAAC,SAAS,KAAK,SAAS,EAAE;wBACzC,OAAO,SAAS,CAAC,CAAC,gCAAgC;qBACnD;oBACD,IAAI,cAAc,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAC1C,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;wBAC5B,IAAI;wBACJ,KAAK,EAAE,WAAW,CAAC,MAAM,EAAE,gBAAgB,CAAC;qBAC7C,CAAC,CACH,CAAC;oBACF,OAAO;wBACL,IAAI,EAAE,OAAO;wBACb,IAAI,EAA0B,CAC5B,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,gBAAgB,CAAC,CACzC;wBACD,KAAK,EAAE,cAAc;wBACrB,eAAe,EAAE,aAAa,CAAC,eAAe;qBAC/C,CAAC;gBACJ,KAAK,OAAO;oBACV,uCACK,aAAa,KAChB,IAAI,EAA0B,CAC5B,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,gBAAgB,CAAC,CACzC,CAAC,2BAA2B;wBAC7B;aACL;SACF;QACD,KAAK,sBAAsB,CAAC,CAAC;YAC3B,MAAM,aAAa,GAA6C,MAAM,CAAC;YACvE,QAAQ,aAAa,CAAC,IAAI,EAAE;gBAC1B,KAAK,OAAO;oBACV,OAAO,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;gBAC5D,KAAK,OAAO;oBACV,OAAO,gCAEF,aAAa,KAChB,IAAI,EAAE,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,gBAAgB,CAAC,GAC/C,CAAC;aACL;YACD,MAAM,CAAC,kBAAkB;SAC1B;QACD,KAAK,MAAM,CAAC,CAAC;YACX,6EAA6E;YAC7E,kCAAkC;YAClC,WAAW;YACX,IAAI,aAAa,GAA6B,MAAM,CAAC;YACrD,IAAI,QAAQ,GAA0B,CACpC,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,gBAAgB,CAAC,CACzC,CAAC,CAAC,qBAAqB;YACxB,QAAQ,aAAa,CAAC,IAAI,EAAE;gBAC1B,KAAK,OAAO;oBACV,OAAO;wBACL,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE;4BACL,IAAI,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE;yBAC9C;wBACD,eAAe,EAAE,aAAa,CAAC,eAAe;qBAC/C,CAAC;gBACJ,KAAK,OAAO;oBACV,QAAQ,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE;wBAChC,KAAK,qBAAqB;4BACxB,OAAO;gCACL,IAAI,EAAE,QAAQ;gCACd,IAAI,EAAE,OAAO;gCACb,KAAK,EAAE;oCACL,IAAI,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE;iCAC1C;gCACD,eAAe,EAAE,EAAE;6BACpB,CAAC;wBACJ,KAAK,kBAAkB;4BACrB,OAAO;gCACL,IAAI,EAAE,QAAQ;gCACd,IAAI,EAAE,OAAO;gCACb,KAAK,EAAE;oCACL,IAAI,EAAE,kBAAkB;oCACxB,WAAW,EAAE,aAAa,CAAC,KAAK,CAAC,WAAW;oCAC5C,GAAG,EAAE,aAAa,CAAC,KAAK,CAAC,GAAG;iCAC7B;6BACF,CAAC;wBACJ,KAAK,2BAA2B;4BAC9B,IAAI,YAAY,GAAG,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;4BACvD,IAAI,YAAY,CAAC,SAAS,EAAE,IAAI,QAAQ,CAAC,IAAI,EAAE;gCAC7C,OAAO;oCACL,IAAI,EAAE,QAAQ;oCACd,IAAI,EAAE,OAAO;oCACb,KAAK,EAAE;wCACL,IAAI,EAAE,YAAY;qCACnB;oCACD,eAAe,EAAE,EAAE;iCACpB,CAAC;6BACH;iCAAM;gCACL,OAAO;oCACL,IAAI,EAAE,QAAQ;oCACd,IAAI,EAAE,OAAO;oCACb,KAAK,EAAE;wCACL,IAAI,EAAE,kBAAkB;wCACxB,WAAW,EAAE,MAAM;wCACnB,GAAG,EAAE,UAAU,CAAC,WAAW,CAAC,YAAY,CAAC;qCAC1C;iCACF,CAAC;6BACH;wBACH;4BACE,OAAO;gCACL,IAAI,EAAE,QAAQ;gCACd,IAAI,EAAE,OAAO;gCACb,KAAK,EAAE,aAAa,CAAC,KAAK;6BAC3B,CAAC;qBACL;aACJ;SACF;QACD,KAAK,OAAO,CAAC,CAAC;YACZ,IAAI,aAAa,GAA8B,MAAM,CAAC;YACtD,QAAQ,aAAa,CAAC,IAAI,EAAE;gBAC1B,KAAK,OAAO;oBACV,IAAI,aAAa,CAAC,SAAS,KAAK,SAAS,EAAE;wBACzC,OAAO,SAAS,CAAC,CAAC,gCAAgC;qBACnD;oBACD,IAAI,cAAc,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CACpD,WAAW,CAAC,MAAM,EAAE,gBAAgB,CAAC,CACtC,CAAC;oBACF,OAAO;wBACL,IAAI,EAAE,OAAO;wBACb,IAAI,EAA0B,CAC5B,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,gBAAgB,CAAC,CACzC;wBACD,KAAK,EAAE,cAAc;wBACrB,eAAe,EAAE,aAAa,CAAC,eAAe;qBAC/C,CAAC;gBACJ,KAAK,OAAO;oBACV,uCACK,aAAa,KAChB,IAAI,EAA0B,CAC5B,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,gBAAgB,CAAC,CACzC,IACD;aACL;SACF;QACD;YACE,OAAgC,MAAM,CAAC,CAAC,iBAAiB;KAC5D;AACH,CAAC;AAxND,kCAwNC;AAED,4BAA4B;AAC5B,SAAgB,qBAAqB,CACnC,QAA0B,EAC1B,gBAAwC;IAExC,IAAI,QAAQ,CAAC,YAAY,KAAK,KAAK,EAAE;QACnC,OAAO,QAAQ,CAAC;KACjB;IACD,QAAQ,QAAQ,CAAC,IAAI,EAAE;QACrB,KAAK,UAAU,CAAC;QAChB,KAAK,aAAa;YAChB,uCACK,QAAQ,KACX,YAAY,EAAE,KAAK,EACnB,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,iCACzC,QAAQ,KACX,KAAK,EAAE,WAAW,CAAC,QAAQ,CAAC,KAAK,EAAE,gBAAgB,CAAC,IACpD,CAAC,IACH;QACJ;YACE,uCACK,QAAQ,KACX,YAAY,EAAE,KAAK,IACnB;KACL;AACH,CAAC;AAxBD,sDAwBC;AAED,4BAA4B;AAC5B,SAAgB,gBAAgB,CAC9B,QAAqB,EACrB,gBAAwC;IAExC,IAAI,QAAQ,CAAC,YAAY,KAAK,KAAK,EAAE;QACnC,OAAO,QAAQ,CAAC;KACjB;IACD,uCACK,QAAQ,KACX,YAAY,EAAE,KAAK,EACnB,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,iCACzC,QAAQ,KACX,KAAK,EAAE,WAAW,CAAC,QAAQ,CAAC,KAAK,EAAE,gBAAgB,CAAC,IACpD,CAAC,IACH;AACJ,CAAC;AAfD,4CAeC;AAED,4BAA4B;AAC5B,SAAgB,uBAAuB,CACrC,QAA4B,EAC5B,gBAAwC;IAExC,IAAI,QAAQ,CAAC,YAAY,KAAK,KAAK,EAAE;QACnC,OAAO,QAAQ,CAAC;KACjB;IACD,QAAQ,QAAQ,CAAC,IAAI,EAAE;QACrB,KAAK,QAAQ,CAAC;QACd,KAAK,QAAQ;YACX,uCACK,QAAQ,KACX,YAAY,EAAE,KAAK,EACnB,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,iCACzC,QAAQ,KACX,KAAK,EAAE,WAAW,CAAC,QAAQ,CAAC,KAAK,EAAE,gBAAgB,CAAC,IACpD,CAAC,IACH;QACJ,KAAK,UAAU;YACb,uCACK,QAAQ,KACX,YAAY,EAAE,KAAK,EACnB,UAAU,EAAE,SAAS,IACrB;QACJ;YACE,uCACK,QAAQ,KACX,YAAY,EAAE,KAAK,IACnB;KACL;AACH,CAAC;AA9BD,0DA8BC"}