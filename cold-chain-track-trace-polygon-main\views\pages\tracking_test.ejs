<!DOCTYPE html>
<html lang="en">
<head>
  <%- include('../partials/head'); %>
</head>
<body class="container">

<header>
  <%- include('../partials/header'); %>
</header>

<main>
  <div class="jumbotron">
    <h1>Smart Cooler Box Tracking</h1>
    <p>Track your shipment using the package ID</p>
  </div>
  <div class="container">
    <% if  (!data_order) { %>
    <div class="row h-100 justify-content-center align-items-center">    
      <div class="col-sm-12 text-center">
        <h3>Need the status of your shipment or proof of delivery? Enter your tracking number or reference number below. </p>
      </div>
      <div class="col-sm-6 text-center">
        <form action="/tracking" method="POST">
          <div class="form-group row">
            <div class="input-group">
                <input type="text" class="form-control" name="trackingID" placeholder="tracking number...">
            </div>
          </div>
          <div class="form-group row">
            <button class="form-control btn btn-sm btn-secondary" type="submit">Track</button>
          </div>
            
        </form>
      </div>
    </div>
    <% } else { %>
    <hr>
    <div class="d-flex justify-content-between">
      <div><h3>Package Timeline for Order <%= package_id %></h3></div>
      <div> <a class="nav-link" href="/tracking">Track another package</a></div>
    </div>

      <!-- timeline item 1 -->
      <div class="row">
        <!-- timeline item 1 left dot -->
        <div class="col-auto text-center flex-column d-none d-sm-flex">
          <div class="row h-50">
            <div class="col">&nbsp;</div>
            <div class="col">&nbsp;</div>
          </div>
          <h5 class="m-2">
            <span class="badge badge-pill bg-light border">&nbsp;</span>
          </h5>
          <div class="row h-50">
            <div class="col border-right">&nbsp;</div>
            <div class="col">&nbsp;</div>
          </div>
        </div>
        <!-- timeline item 1 event content -->
        <div class="col py-2">
          <div class="card">
            <div class="card-body">
              <div class="float-right text-muted">Mon, Jan 9th 2019 7:00 AM</div>
              <h4 class="card-title">Order Placed</h4>
              <p class="card-text">Ready for collection.</p>
            </div>
          </div>
        </div>
      </div>
      <!--/row-->
      <!-- timeline item 2 -->
      <div class="row">
        <div class="col-auto text-center flex-column d-none d-sm-flex">
          <div class="row h-50">
            <div class="col border-right">&nbsp;</div>
            <div class="col">&nbsp;</div>
          </div>
          <h5 class="m-2">
            <span class="badge badge-pill bg-light border">&nbsp;</span>
          </h5>
          <div class="row h-50">
            <div class="col border-right">&nbsp;</div>
            <div class="col">&nbsp;</div>
          </div>
        </div>
        <div class="col py-2">
          <div class="card">
            <div class="card-body">
              <div class="float-right">Tue, Jan 10th 2019 8:30 AM</div>
              <h4 class="card-title">Event 1</h4>
              <p class="card-text">Picked up by courier.</p>
            </div>
          </div>
        </div>
      </div>
      <!--/row-->
      <!-- timeline item 3 -->
      <div class="row">
        <div class="col-auto text-center flex-column d-none d-sm-flex">
          <div class="row h-50">
            <div class="col border-right">&nbsp;</div>
            <div class="col">&nbsp;</div>
          </div>
          <h5 class="m-2">
            <span class="badge badge-pill bg-light border">&nbsp;</span>
          </h5>
          <div class="row h-50">
            <div class="col border-right">&nbsp;</div>
            <div class="col">&nbsp;</div>
          </div>
        </div>
        <div class="col py-2">
          <div class="card">
            <div class="card-body">
              <div class="float-right text-muted">Wed, Jan 11th 2019 8:30 AM</div>
              <h4 class="card-title">Exception</h4>
              <p>Temperature range exceeded.</p>
            </div>
          </div>
        </div>
      </div>
      <!--/row-->
      <!-- timeline item 4 -->
      <div class="row">
        <div class="col-auto text-center flex-column d-none d-sm-flex">
          <div class="row h-50">
            <div class="col border-right">&nbsp;</div>
            <div class="col">&nbsp;</div>
          </div>
          <h5 class="m-2">
            <span class="badge badge-pill bg-light border">&nbsp;</span>
          </h5>
          <div class="row h-50">
            <div class="col">&nbsp;</div>
            <div class="col">&nbsp;</div>
          </div>
        </div>
        <div class="col py-2">
          <div class="card">
            <div class="card-body">
              <div class="float-right text-muted">Thu, Jan 12th 2019 11:30 AM</div>
              <h4 class="card-title">Event</h4>
              <p>Rceived by recipient.</p>
            </div>
          </div>
        </div>
      </div>
      <!--/row-->
    </div>
    <!--container-->
    <% } %>
    
</main>

<footer>
  <%- include('../partials/footer'); %>
</footer>

</body>
</html>